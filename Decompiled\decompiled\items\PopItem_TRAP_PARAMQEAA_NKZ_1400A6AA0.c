/*
 * Function: ?PopItem@_TRAP_PARAM@@QEAA_NK@Z
 * Address: 0x1400A6AA0
 */

char __fastcall _TRAP_PARAM::PopItem(_TRAP_PARAM *this, unsigned int dwTrapSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _TRAP_PARAM *v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = dwTrapSerial;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 20; ++j )
  {
    if ( _TRAP_PARAM::_param::isLoad((_TRAP_PARAM::_param *)v7 + j) && v7->m_Item[j].dwSerial == v8 )
    {
      _TRAP_PARAM::_param::init((_TRAP_PARAM::_param *)v7 + j);
      --v7->m_nCount;
      return 1;
    }
  }
  return 0;
}
