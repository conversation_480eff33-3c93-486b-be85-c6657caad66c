/*
 * Function: ?SearchEmptyMonster@@YAPEAVCMonster@@_N@Z
 * Address: 0x140148F20
 */

CMonster *__fastcall SearchEmptyMonster(bool bWithoutFail)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *result; // rax@7
  __int64 v4; // [sp+0h] [bp-48h]@1
  __int64 v5; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@4
  CMonster *v7; // [sp+30h] [bp-18h]@14
  CMonster *v8; // [sp+38h] [bp-10h]@27
  bool v9; // [sp+50h] [bp+8h]@1

  v9 = bWithoutFail;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0i64;
  for ( j = 0; j < 30000; ++j )
  {
    if ( !*((_BYTE *)g_Monster + 6424 * j + 24) )
      return (CMonster *)((char *)g_Monster + 6424 * j);
  }
  if ( v9 )
  {
    for ( j = 0; j < 30000; ++j )
    {
      v7 = (CMonster *)((char *)g_Monster + 6424 * j);
      if ( !v7->m_bLive )
        return v7;
      if ( !v7->m_pCurMap->m_pMapSet->m_nMapType
        && (signed int)(unsigned __int8)CMonsterHierarchy::ChildKindCount(&v7->m_MonHierarcy) <= 0
        && !CMonsterHierarchy::GetParent(&v7->m_MonHierarcy)
        && v7->m_pMonRec->m_bMonsterCondition != 1
        && !CMonster::GetEmotionState(v7) )
      {
        CMonster::Destroy(v7, 1, 0i64);
        return v7;
      }
    }
    for ( j = 0; j < 30000; ++j )
    {
      v8 = (CMonster *)((char *)g_Monster + 6424 * j);
      if ( !v8->m_bLive )
        return v8;
      if ( !v8->m_pCurMap->m_pMapSet->m_nMapType
        && (signed int)(unsigned __int8)CMonsterHierarchy::ChildKindCount(&v8->m_MonHierarcy) <= 0
        && !CMonsterHierarchy::GetParent(&v8->m_MonHierarcy)
        && v8->m_pMonRec->m_bMonsterCondition != 1
        && !v8->m_pTargetChar )
      {
        CMonster::Destroy(v8, 1, 0i64);
        return v8;
      }
    }
    result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
