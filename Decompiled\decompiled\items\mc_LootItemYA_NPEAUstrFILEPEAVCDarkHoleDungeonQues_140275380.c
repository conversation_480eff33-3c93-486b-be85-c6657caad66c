/*
 * Function: ?mc_LootItem@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140275380
 */

bool __fastcall mc_LootItem(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // rax@17
  __int64 v7; // [sp+0h] [bp-178h]@1
  char poutszWord; // [sp+50h] [bp-128h]@4
  int v9; // [sp+D4h] [bp-A4h]@6
  _base_fld *v10; // [sp+D8h] [bp-A0h]@8
  int pnoutVal; // [sp+E4h] [bp-94h]@10
  _react_obj poutReactObject; // [sp+108h] [bp-70h]@14
  int v13; // [sp+134h] [bp-44h]@16
  __add_loot_item *v14; // [sp+140h] [bp-38h]@19
  __add_loot_item *v15; // [sp+148h] [bp-30h]@16
  __int64 v16; // [sp+150h] [bp-28h]@4
  __add_loot_item *v17; // [sp+158h] [bp-20h]@17
  unsigned __int64 v18; // [sp+160h] [bp-18h]@4
  strFILE *fstra; // [sp+180h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+188h] [bp+10h]@1
  char *v21; // [sp+190h] [bp+18h]@1

  v21 = pszoutErrMsg;
  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v7;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = -2i64;
  v18 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( strFILE::word(fstra, &poutszWord) )
  {
    v9 = GetItemTableCode(&poutszWord);
    if ( v9 == -1 )
    {
      result = _false(fstra, pSetupa);
    }
    else
    {
      v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v9, &poutszWord);
      if ( v10 )
      {
        if ( strFILE::word(fstra, &pnoutVal) )
        {
          if ( pnoutVal > 0 )
          {
            _react_obj::_react_obj(&poutReactObject);
            if ( GetReactObject(fstra, pSetupa, &poutReactObject, 0, 6u, v21, 0i64) )
            {
              v13 = pSetupa->m_pCurLoadMission->nLootItemNum;
              v15 = (__add_loot_item *)operator new(0x38ui64);
              if ( v15 )
              {
                __add_loot_item::__add_loot_item(v15);
                v17 = (__add_loot_item *)v6;
              }
              else
              {
                v17 = 0i64;
              }
              v14 = v17;
              pSetupa->m_pCurLoadMission->pLootItem[v13] = v17;
              pSetupa->m_pCurLoadMission->pLootItem[v13]->byItemTableCode = v9;
              pSetupa->m_pCurLoadMission->pLootItem[v13]->pItemFld = v10;
              pSetupa->m_pCurLoadMission->pLootItem[v13]->dwDur = pnoutVal;
              _react_obj::copy(&pSetupa->m_pCurLoadMission->pLootItem[v13]->ReactObj, &poutReactObject);
              ++pSetupa->m_pCurLoadMission->nLootItemNum;
              result = 1;
            }
            else
            {
              result = _false(fstra, pSetupa);
            }
          }
          else
          {
            result = _false(fstra, pSetupa);
          }
        }
        else
        {
          result = _false(fstra, pSetupa);
        }
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}
