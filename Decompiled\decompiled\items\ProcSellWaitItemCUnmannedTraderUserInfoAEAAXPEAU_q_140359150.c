/*
 * Function: ?ProcSellWaitItem@CUnmannedTraderUserInfo@@AEAAXPEAU_qry_case_unmandtrader_log_in_proc_update_complete@@EPEAVCLogFile@@@Z
 * Address: 0x140359150
 */

void __fastcall CUnmannedTraderUserInfo::ProcSellWaitItem(CUnmannedTraderUserInfo *this, _qry_case_unmandtrader_log_in_proc_update_complete *pkResult, char byGroupType, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v6; // rax@7
  CUnmannedTraderRegistItemInfo *v7; // rax@8
  CUnmannedTraderRegistItemInfo *v8; // rax@8
  CUnmannedTraderRegistItemInfo *v9; // rax@11
  CUnmannedTraderRegistItemInfo *v10; // rax@11
  CUnmannedTraderRegistItemInfo *v11; // rax@11
  CUnmannedTraderRegistItemInfo *v12; // rax@11
  CUnmannedTraderRegistItemInfo *v13; // rax@13
  CUnmannedTraderRegistItemInfo *v14; // rax@14
  CUnmannedTraderRegistItemInfo *v15; // rax@14
  CUnmannedTraderRegistItemInfo *v16; // rax@14
  CUnmannedTraderRegistItemInfo *v17; // rax@14
  CUnmannedTraderRegistItemInfo *v18; // rax@14
  CUnmannedTraderRegistItemInfo *v19; // rax@14
  CUnmannedTraderRegistItemInfo *v20; // rax@14
  unsigned __int16 v21; // ax@15
  __int64 v22; // [sp+0h] [bp-168h]@1
  char *byStorageInx; // [sp+20h] [bp-148h]@8
  char *strErrorCodePos; // [sp+28h] [bp-140h]@17
  _unmannedtrader_Sell_Wait_item_inform_zocl Dst; // [sp+40h] [bp-128h]@4
  char v26[28]; // [sp+D8h] [bp-90h]@4
  char v27; // [sp+F4h] [bp-74h]@4
  CPlayer *v28; // [sp+F8h] [bp-70h]@4
  int j; // [sp+100h] [bp-68h]@4
  char pbyType; // [sp+114h] [bp-54h]@15
  char v31; // [sp+115h] [bp-53h]@15
  unsigned __int8 k; // [sp+124h] [bp-44h]@15
  char *v33; // [sp+130h] [bp-38h]@8
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v34; // [sp+138h] [bp-30h]@8
  __int64 tResultTime; // [sp+140h] [bp-28h]@8
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v36; // [sp+148h] [bp-20h]@8
  unsigned __int64 v37; // [sp+150h] [bp-18h]@4
  CUnmannedTraderUserInfo *v38; // [sp+170h] [bp+8h]@1
  _qry_case_unmandtrader_log_in_proc_update_complete *v39; // [sp+178h] [bp+10h]@1
  CLogFile *pkLoggera; // [sp+188h] [bp+20h]@1

  pkLoggera = pkLogger;
  v39 = pkResult;
  v38 = this;
  v4 = &v22;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v37 = (unsigned __int64)&v22 ^ _security_cookie;
  _unmannedtrader_Sell_Wait_item_inform_zocl::_unmannedtrader_Sell_Wait_item_inform_zocl(&Dst);
  memset_0(&Dst, 0, 0x71ui64);
  memset_0(v26, 0, 0xAui64);
  v27 = 0;
  v28 = CUnmannedTraderUserInfo::FindOwner(v38);
  for ( j = 0; j < 20; ++j )
  {
    v6 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
           &v38->m_vecLoadItemInfo,
           j);
    if ( CUnmannedTraderRegistItemInfo::IsSellWait(v6) )
    {
      v33 = &v26[(unsigned __int8)Dst.byNum];
      v34 = &v38->m_vecLoadItemInfo;
      v7 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v38->m_vecLoadItemInfo,
             j);
      tResultTime = CUnmannedTraderRegistItemInfo::GetResultTime(v7);
      v36 = &v38->m_vecLoadItemInfo;
      v8 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v38->m_vecLoadItemInfo,
             j);
      byStorageInx = v33;
      v27 = CUnmannedTraderRegistItemInfo::SellWaitItem(v8, v38->m_wInx, pkLoggera, tResultTime, v33);
      if ( v27 != 37 && v27 != 94 && v27 != 34 )
      {
        if ( v27 )
        {
          v13 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v38->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::ClearToWaitState(v13);
        }
        else
        {
          v14 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v38->m_vecLoadItemInfo,
                  j);
          Dst.List[(unsigned __int8)Dst.byNum].wItemSerial = CUnmannedTraderRegistItemInfo::GetItemSerial(v14);
          v15 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v38->m_vecLoadItemInfo,
                  j);
          Dst.List[(unsigned __int8)Dst.byNum].dwSellDalant = CUnmannedTraderRegistItemInfo::GetPrice(v15);
          v16 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v38->m_vecLoadItemInfo,
                  j);
          Dst.List[(unsigned __int8)Dst.byNum].dwTax = CUnmannedTraderRegistItemInfo::GetTax(v16);
          Dst.List[(unsigned __int8)Dst.byNum].dwSellDalant -= Dst.List[(unsigned __int8)Dst.byNum].dwTax;
          Dst.dwTotalSellDalant += Dst.List[(unsigned __int8)Dst.byNum].dwSellDalant;
          Dst.dwTotalTaxDalant += Dst.List[(unsigned __int8)Dst.byNum].dwTax;
          ++Dst.byNum;
          v39->List[v39->wNum].byProcUpdate = 90;
          v17 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v38->m_vecLoadItemInfo,
                  j);
          v39->List[v39->wNum].dwBuyer = CUnmannedTraderRegistItemInfo::GetBuyerSerial(v17);
          v18 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v38->m_vecLoadItemInfo,
                  j);
          v39->List[v39->wNum].dwRegistSerial = CUnmannedTraderRegistItemInfo::GetRegistSerial(v18);
          v39->List[v39->wNum++].byUpdateState = 3;
          v19 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v38->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::Clear(v19);
          v20 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v38->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::SetState(v20, 3);
        }
      }
      else
      {
        v39->List[v39->wNum].byProcUpdate = 37;
        v9 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
               &v38->m_vecLoadItemInfo,
               j);
        v39->List[v39->wNum].dwBuyer = CUnmannedTraderRegistItemInfo::GetBuyerSerial(v9);
        v10 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                &v38->m_vecLoadItemInfo,
                j);
        v39->List[v39->wNum].dwRegistSerial = CUnmannedTraderRegistItemInfo::GetRegistSerial(v10);
        v39->List[v39->wNum++].byUpdateState = 8;
        v11 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                &v38->m_vecLoadItemInfo,
                j);
        CUnmannedTraderRegistItemInfo::Clear(v11);
        v12 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                &v38->m_vecLoadItemInfo,
                j);
        CUnmannedTraderRegistItemInfo::SetState(v12, 8);
      }
    }
  }
  Dst.dwCurInvenDalant = CPlayerDB::GetDalant(&v28->m_Param);
  pbyType = 30;
  v31 = 26;
  v21 = _unmannedtrader_Sell_Wait_item_inform_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v38->m_wInx, &pbyType, &Dst.byNum, v21);
  for ( k = 0; k < (signed int)(unsigned __int8)Dst.byNum; ++k )
  {
    strErrorCodePos = "CUnmannedTraderUserInfo::ProcSellWaitItem()";
    LOBYTE(byStorageInx) = 1;
    CPlayer::Emb_DelStorage(v28, 0, v26[k], 0, 1, "CUnmannedTraderUserInfo::ProcSellWaitItem()");
  }
}
