/*
 * RF Online Server Reverse Engineering Analysis
 * Compatible with Visual Studio 2022
 * Based on decompiled C code analysis
 */

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <cstdint>

// ============================================================================
// 1. ANTI-CHEAT MECHANISMS
// ============================================================================

namespace RFOnline {
namespace AntiCheat {

// HackShield/nProtect GameGuard Integration
class CHackShieldExSystem {
public:
    struct BASE_HACKSHEILD_PARAM {
        void* vfptr;
        virtual bool IsLogPass() = 0;
        virtual void OnLoop() = 0;
        virtual void OnConnect(int nIndex) = 0;
        virtual bool OnCheckSession_FirstVerify(int n) = 0;
        virtual bool OnRecvSession(void* mgr, int nIndex, char byProtocol, uint64_t tSize, char* pMsg) = 0;
    };

    struct HACKSHEILD_PARAM_ANTICP : public BASE_HACKSHEILD_PARAM {
        bool OnRecvSession_ServerCheckSum_Request(int nIndex);
        bool OnRecvSession_ClientCheckSum_Response(uint64_t tSize, char* pMsg);
    };

private:
    BASE_HACKSHEILD_PARAM** m_ppNodeArray;
    bool m_bInit;
    bool m_bActive;
    uint32_t m_dwCurrentCheckIndex;
    
public:
    CHackShieldExSystem();
    ~CHackShieldExSystem();
    
    bool Init();
    void OnLoop();
    void OnConnectSession(int n);
    void OnLoopSession(int n);
    bool OnCheckSession_FirstVerify(int n);
    BASE_HACKSHEILD_PARAM* GetParam(int n);
};

// Speed Hack Detection
struct SpeedHackDetection {
    uint32_t m_dwSpeedHackKey[4];      // 16-byte key for speed validation
    uint32_t m_dwSendSpeedHackTime;    // Last speed check timestamp
    uint32_t m_dwSpeedHackCount;       // Number of speed checks sent
    
    static void SendSpeedHackCheckMsg(int nIndex);
    static uint32_t* CalcCodeKey(uint32_t* pdwCode);
};

// Mining Speed Controls (Anti-cheat for resource gathering)
struct MiningSpeedControl {
    static float MINE_SPEED_RATE;
    static float PCBANG_PRIMIUM_FAVOR_MINING_SPEED;
    
    static bool ct_minespeed(void* pPlayer);      // Set mining speed
    static bool ct_pcminespeed(void* pPlayer);    // Set PC bang mining speed
};

} // namespace AntiCheat

// ============================================================================
// 2. PACKET STRUCTURES & NETWORK PROTOCOLS
// ============================================================================

namespace Network {

// Message Header Structure
struct _MSG_HEADER {
    uint16_t m_wSize;        // Total packet size
    uint8_t  m_byType1;      // Primary packet type
    uint8_t  m_byType2;      // Secondary packet type
    uint32_t m_dwSerial;     // Packet serial number
    // Additional fields may follow
};

// Packet Encryption (FG = nProtect GameGuard)
namespace Encryption {
    // FG Encryption Functions (from GameGuard)
    int _CcrFG_rs_EncryptPacket(void* context, uint8_t* data, int size);
    int _CcrFG_rs_DecryptPacket(void* context, uint8_t* data, int size);
    
    // Custom Encryption
    void EnCryptString(char* pStr, int nSize, char byPlus, uint16_t wCryptKey);
    
    // CryptoPP Integration
    class CCryptor {
        void* m_pkParam;
    public:
        bool Encrypt(const char* pText, uint64_t tLength, char* pCipherText, uint64_t tCipherTextLength);
        bool Decrypt(const char* pCipherText, uint64_t tLength, char* pText, uint64_t tTextLength);
    };
}

// Network Configuration
struct _NET_TYPE_PARAM {
    char m_szModuleName[128];
    bool m_bRealSockCheck;
    bool m_bSystemLogFile;
    bool m_bServer;
    uint8_t m_byRecvThreadNum;
    uint8_t m_byRecvSleepTime;
    uint16_t m_wSocketMaxNum;
    bool m_bSvrToS;
    bool m_bOddMsgWriteLog;
    uint32_t m_dwSendBufferSize;
    uint32_t m_dwRecvBufferSize;
    bool m_bAnSyncConnect;
};

// Connection Types
enum class ConnectionType {
    CLIENT_LINE = 0,    // Player connections (port 27555)
    ACCOUNT_LINE = 1,   // Account server (port 27555)
    WEB_LINE = 2,       // Web server (port 27556)
    BILLING_LINE = 3    // Billing server
};

// Socket Structure
struct _socket {
    uint32_t m_dwSpeedHackKey[4];
    uint32_t m_dwSendSpeedHackTime;
    uint32_t m_dwSpeedHackCount;
    bool m_bActive;
    // Additional socket data
};

// Network Manager
class CNetworkEX {
public:
    void SetPassablePacket(uint32_t dwProID, char byHeader1, char byHeader2);
    bool EnterWorldRequest(int n, _MSG_HEADER* pMsgHeader, char* pBuf);
    void Close(int nReason, int nIndex, int nFlag, void* pData);
    
private:
    void* m_pProcess[4];  // Process handlers for each connection type
};

} // namespace Network

// ============================================================================
// 3. GAME BALANCE FORMULAS
// ============================================================================

namespace GameBalance {

// Experience Calculation
class ExperienceSystem {
public:
    static void CalcExp(void* pPlayer, void* pTarget, int nDamage, void* pPartyNotify);
    static void CalcDefExp(void* pAnimus, void* pAttacker, int nDamage);
    static void CalcAttExp(void* pAnimus, void* pAttack);
    
    // Experience modifiers
    static bool CheckExpAfterAttack(void* pPlayer, int nDamagedObjNum, void* pDamageList, void* pPartyNotify);
    static double GetExp(void* pPlayerDB);
};

// Damage Calculation System
class DamageSystem {
public:
    static int GetAttackDamPoint(void* pAttacker, int nAttPnt, int nAttPart, int nTolType, void* pTarget, bool bBackAttack);
    static void CalcAvgDamage(void* pAttack);
    static void AreaDamageProc(void* pAttack, int nLimitRadius, int nAttPower, float* pTargetPos, int nEffAttPower, bool bUseEffBullet);
    
    // Damage effects
    static bool DE_DamStun(void* pActChar, void* pTargetChar, float fEffectValue, char* byRet);
    static bool DE_ContDamageTimeInc(void* pActChar, void* pTargetChar, float fEffectValue, char* byRet);
    static bool DE_AllContDamageForceRemove(void* pActChar, void* pTargetChar, float fEffectValue, char* byRet);
};

// Drop Rate System
class DropSystem {
public:
    static uint32_t GetMonsterDropRate(void* pMonsterInfo, int iDiffLevel);
    static bool Drop(void* pItemDropMgr, int nCnt);
    static bool FrontDrop(void* pItemDropMgr);
    static void SetDropItem(void* pHolyStone);
    static void DropItem(void* pHolyKeeper);
    
    // Drop rate modifiers based on level difference
    struct DropRateModifiers {
        uint32_t m_iMonsterLootRateSame;           // Same level
        uint32_t m_iMonsterLootingRateUp[11];      // Higher level (1-10+)
        uint32_t m_iMonsterLootingRateDown[11];    // Lower level (1-10+)
    };
};

} // namespace GameBalance

// ============================================================================
// 4. SECURITY VULNERABILITIES IDENTIFIED
// ============================================================================

namespace SecurityAnalysis {

// Buffer Overflow Vulnerabilities
struct BufferOverflowRisks {
    // sprintf without bounds checking - CRITICAL
    static void UnsafeSprintf_Example() {
        // Found in: Insert_PvpOrderViewInfo, Insert_PvpPointLimitInfoRecord
        // sprintf(&Dest, "{ CALL pInsert_PvpOrderView( %d ) }", dwSerial);
        // Risk: No bounds checking on Dest buffer
    }
    
    // strcpy without bounds checking - HIGH
    static void UnsafeStringCopy_Example() {
        // Found in: CUnmannedTraderSubClassInfo::Copy
        // strcpy_0(v6->m_szName, lhs->m_szName);
        // Risk: No validation of source string length
    }
    
    // memcpy with user-controlled size - HIGH
    static void UnsafeMemcpy_Example() {
        // Found in: WriteFile functions
        // memcpy_0(v9 + 68, Str, (unsigned int)Size);
        // Risk: Size parameter not validated
    }
};

// SQL Injection Vulnerabilities
struct SQLInjectionRisks {
    // Direct string formatting in SQL - CRITICAL
    static void UnsafeSQLFormatting_Example() {
        // Found in: GetUseCashQueryStr
        // sprintf_s(..., "exec prc_rfonline_use @s_userid = '%s'", rParam->in_szAcc);
        // Risk: User input directly inserted into SQL without sanitization
    }
};

// Authentication Bypass Risks
struct AuthenticationRisks {
    // Client version check bypass - MEDIUM
    static void ClientVersionBypass_Example() {
        // Found in: EnterWorldRequest
        // if ( strcmp_0(CMainThread::ms_szClientVerCheck, "X") )
        // Risk: Hardcoded bypass value "X"
    }
    
    // Speed hack detection bypass - MEDIUM
    static void SpeedHackBypass_Example() {
        // Found in: Speed hack detection
        // Uses predictable random() for key generation
        // Risk: Weak randomness allows prediction
    }
};

// Memory Corruption Risks
struct MemoryCorruptionRisks {
    // Stack buffer overflow - HIGH
    static void StackOverflow_Example() {
        // Found in: _AtlVerifyStackAvailable
        // Insufficient stack space validation
        // Risk: Stack corruption possible
    }
    
    // Use after free - MEDIUM
    static void UseAfterFree_Example() {
        // Found in: Various operator delete calls
        // Risk: Potential use of freed memory
    }
};

} // namespace SecurityAnalysis

} // namespace RFOnline
