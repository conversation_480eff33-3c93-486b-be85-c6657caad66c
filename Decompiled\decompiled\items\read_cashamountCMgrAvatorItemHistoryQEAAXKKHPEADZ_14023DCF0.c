/*
 * Function: ?read_cashamount@CMgrAvatorItemHistory@@QEAAXKKHPEAD@Z
 * Address: 0x14023DCF0
 */

void __fastcall CMgrAvatorItemHistory::read_cashamount(CMgrAvatorItemHistory *this, unsigned int dwAC, unsigned int dwAV, int nCash, char *pFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  CMgrAvatorItemHistory *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  sData[0] = 0;
  v8 = nCash;
  sprintf(sData, "[READ_CASH] : AC:%u AV:%u Cash:%u\r\n", dwAC, dwAV);
  CMgrAvatorItemHistory::WriteFile(v9, pFileName, sData);
}
