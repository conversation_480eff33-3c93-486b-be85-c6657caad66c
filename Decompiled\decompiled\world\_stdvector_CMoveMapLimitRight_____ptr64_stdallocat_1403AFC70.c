/*
 * Function: _std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____::_1_::catch$0
 * Address: 0x1403AFC70
 */

void __fastcall __noreturn std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____::_1_::catch_0(__int64 a1, __int64 a2)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Tidy(*(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 176));
  CxxThrowException_0(0i64, 0i64);
}
