/*
 * Function: j_??$_Unchecked_move_backward@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@@stdext@@YAPEAPEAVCMoveMapLimitRight@@PEAPEAV1@00@Z
 * Address: 0x14000807B
 */

CMoveMapLimitRight **__fastcall stdext::_Unchecked_move_backward<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Dest)
{
  return stdext::_Unchecked_move_backward<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(_First, _Last, _Dest);
}
