/*
 * Function: ?SetAllItemState@CUnmannedTraderUserInfo@@AEAAXEE@Z
 * Address: 0x14035FF90
 */

void __fastcall CUnmannedTraderUserInfo::SetAllItemState(CUnmannedTraderUserInfo *this, char byState, char byMaxCnt)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  CUnmannedTraderUserInfo *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  char v10; // [sp+50h] [bp+18h]@1

  v10 = byMaxCnt;
  v9 = byState;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(&v8->m_vecRegistItemInfo) >= (unsigned __int8)byMaxCnt )
  {
    for ( j = 0; j < (unsigned __int8)v10; ++j )
    {
      v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v8->m_vecRegistItemInfo,
             j);
      CUnmannedTraderRegistItemInfo::SetState(v5, v9);
    }
  }
}
