/*
 * Function: ?SetDummyPoint@CDummyDraw@@QEAAXPEAVCMapData@@PEAMHPEAVCRect@@@Z
 * Address: 0x14019C560
 */

void __fastcall CDummyDraw::SetDummyPoint(CDummyDraw *this, CMapData *pMap, float *pCenterPos, int nType, CRect *prcWnd)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  _bsp_info *v8; // [sp+20h] [bp-28h]@4
  float v9; // [sp+28h] [bp-20h]@4
  float v10; // [sp+2Ch] [bp-1Ch]@4
  float v11; // [sp+30h] [bp-18h]@4
  float v12; // [sp+34h] [bp-14h]@4
  int j; // [sp+38h] [bp-10h]@4
  CDummyDraw *v14; // [sp+50h] [bp+8h]@1
  float *v15; // [sp+60h] [bp+18h]@1

  v15 = pCenterPos;
  v14 = this;
  v5 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14->m_pMap = pMap;
  v14->m_nType = nType;
  v8 = CMapData::GetBspInfo(pMap);
  v9 = (float)(-0.0 - (float)v8->m_nMapMinSize[0]) + (float)(*v15 - 80.0);
  v10 = (float)v8->m_nMapMaxSize[2] - (float)(v15[2] + 80.0);
  v11 = (float)(-0.0 - (float)v8->m_nMapMinSize[0]) + (float)(*v15 + 80.0);
  v12 = (float)v8->m_nMapMaxSize[2] - (float)(v15[2] - 80.0);
  v14->m_fPosAbs[0] = v9;
  v14->m_fPosAbs[1] = v10;
  v14->m_fPosAbs[2] = v11;
  v14->m_fPosAbs[3] = v10;
  v14->m_fPosAbs[4] = v9;
  v14->m_fPosAbs[5] = v12;
  v14->m_fPosAbs[6] = v11;
  v14->m_fPosAbs[7] = v12;
  for ( j = 0; j < 4; ++j )
  {
    v14->m_fScrNor[2 * j] = (float)(v14->m_fPosAbs[2 * j] * (float)prcWnd->right) / (float)v8->m_nMapSize[0];
    v14->m_fScrNor[2 * j + 1] = (float)(v14->m_fPosAbs[2 * j + 1] * (float)prcWnd->bottom) / (float)v8->m_nMapSize[2];
  }
}
