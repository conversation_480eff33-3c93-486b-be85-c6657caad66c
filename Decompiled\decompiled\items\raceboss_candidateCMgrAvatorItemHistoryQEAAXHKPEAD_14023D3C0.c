/*
 * Function: ?raceboss_candidate@CMgrAvatorItemHistory@@QEAAXHKPEAD@Z
 * Address: 0x14023D3C0
 */

void __fastcall CMgrAvatorItemHistory::raceboss_candidate(CMgrAvatorItemHistory *this, int ncost, unsigned int dwSerial, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@4
  CMgrAvatorItemHistory *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1
  unsigned int v10; // [sp+50h] [bp+18h]@1
  char *pszFileNamea; // [sp+58h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v10 = dwSerial;
  v9 = ncost;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = GetKorLocalTime();
  sprintf(sData, "[RACE BOSS]candidate >> Avator Serial:%d\t$D:%d \t^Time:%d\n", v10, (unsigned int)v9);
  CMgrAvatorItemHistory::WriteFile(v8, pszFileNamea, sData);
}
