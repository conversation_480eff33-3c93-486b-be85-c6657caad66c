/*
 * Function: ?Get@CMoveMapLimitInfoList@@AEAAPEAVCMoveMapLimitInfo@@HHK@Z
 * Address: 0x1403A6020
 */

CMoveMapLimitInfo *__fastcall CMoveMapLimitInfoList::Get(CMoveMapLimitInfoList *this, int iLimitType, int iMapInx, unsigned int dwStoreRecordIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-F8h]@1
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v8; // [sp+20h] [bp-D8h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > _Right; // [sp+38h] [bp-C0h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > v10; // [sp+68h] [bp-90h]@4
  CMoveMapLimitInfo *v11; // [sp+88h] [bp-70h]@6
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > result; // [sp+90h] [bp-68h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > v13; // [sp+A8h] [bp-50h]@4
  CMoveMapLimitInfo *v14; // [sp+C0h] [bp-38h]@7
  __int64 v15; // [sp+C8h] [bp-30h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v16; // [sp+D0h] [bp-28h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *__that; // [sp+D8h] [bp-20h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v18; // [sp+E0h] [bp-18h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v19; // [sp+E8h] [bp-10h]@4
  CMoveMapLimitInfoList *v20; // [sp+100h] [bp+8h]@1
  int iType; // [sp+108h] [bp+10h]@1
  int iMapInxa; // [sp+110h] [bp+18h]@1
  unsigned int dwStoreRecordIndexa; // [sp+118h] [bp+20h]@1

  dwStoreRecordIndexa = dwStoreRecordIndex;
  iMapInxa = iMapInx;
  iType = iLimitType;
  v20 = this;
  v4 = &v7;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = -2i64;
  v8 = (std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v20;
  v16 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::end(&v20->m_vecLimitInfo, &result);
  __that = (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v16;
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    &_Right,
    (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v16->_Mycont);
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&result);
  v18 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::begin(v8, &v13);
  v19 = (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v18;
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    &v10,
    (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v18->_Mycont);
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v13);
  while ( std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator!=(
            &v10,
            &_Right) )
  {
    v11 = *std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator*(&v10);
    if ( CMoveMapLimitInfo::IsEqualLimit(v11, iType, iMapInxa, dwStoreRecordIndexa) )
    {
      v14 = v11;
      std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v10);
      std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&_Right);
      return v14;
    }
    std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator++(&v10);
  }
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v10);
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&_Right);
  return 0i64;
}
