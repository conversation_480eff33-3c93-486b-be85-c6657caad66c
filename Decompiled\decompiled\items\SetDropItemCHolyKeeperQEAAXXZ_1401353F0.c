/*
 * Function: ?SetDropItem@CHoly<PERSON>eeper@@QEAAXXZ
 * Address: 0x1401353F0
 */

void __fastcall CHolyKeeper::SetDropItem(CHolyKeeper *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CEventLootTable::_event_drop *v4; // [sp+20h] [bp-18h]@4
  CHolyKeeper *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_nCurrLootIndex = CMonster::s_idxMonsterLoot[v5->m_pRecordSet->m_dwIndex].nStartRecIndex;
  v5->m_nEndLootIndex = CMonster::s_idxMonsterLoot[v5->m_pRecordSet->m_dwIndex].nEndRecIndex;
  v5->m_nCurrDropIndex = 0;
  v4 = CEventLootTable::GetRecord(qword_1799C66E8, v5->m_pRecordSet->m_strCode);
  if ( v4 )
  {
    v5->m_wMagnifications = v4->wMagnifications;
    v5->m_wRange = v4->wRange;
    v5->m_wDropCntOnce = v4->wDropCntOnce;
    CMyTimer::BeginTimer(&v5->m_tmrDropTime, v4->wDropDelay);
  }
  else
  {
    v5->m_wMagnifications = 1;
    v5->m_wRange = 400;
    v5->m_wDropCntOnce = -1;
    CMyTimer::BeginTimer(&v5->m_tmrDropTime, 0);
  }
}
