/*
 * Function: ?swap@?$TInventory@U_INVENKEY@@@@QEAAXPEAV?$TInvenSlot@U_INVENKEY@@@@0@Z
 * Address: 0x1402D49A0
 */

void __fastcall TInventory<_INVENKEY>::swap(TInventory<_INVENKEY> *this, TInvenSlot<_INVENKEY> *pSrc, TInvenSlot<_INVENKEY> *pDst)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _INVENKEY *v5; // rax@7
  unsigned int v6; // eax@8
  __int64 v7; // [sp+0h] [bp-88h]@1
  _INVENKEY *v8; // [sp+20h] [bp-68h]@4
  _INVENKEY *rhs; // [sp+28h] [bp-60h]@4
  int n; // [sp+30h] [bp-58h]@8
  TInvenSlot<_INVENKEY> v11; // [sp+48h] [bp-40h]@12
  __int64 v12; // [sp+68h] [bp-20h]@4
  unsigned int v13; // [sp+70h] [bp-18h]@8
  TInventory<_INVENKEY> *v14; // [sp+90h] [bp+8h]@1
  TInvenSlot<_INVENKEY> *v15; // [sp+98h] [bp+10h]@1
  TInvenSlot<_INVENKEY> *v16; // [sp+A0h] [bp+18h]@1

  v16 = pDst;
  v15 = pSrc;
  v14 = this;
  v3 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = -2i64;
  v8 = TInvenSlot<_INVENKEY>::get_pitem(pSrc);
  rhs = TInvenSlot<_INVENKEY>::get_pitem(v16);
  if ( v8 && rhs && _INVENKEY::operator==(v8, rhs) )
  {
    v5 = TInvenSlot<_INVENKEY>::get_pitem(v15);
    if ( _INVENKEY::IsOverlapItem(v5) )
    {
      v13 = TInvenSlot<_INVENKEY>::get_overlapnum(v15);
      v6 = TInvenSlot<_INVENKEY>::get_overlapnum(v16);
      n = v6 + v13;
      if ( (signed int)(v6 + v13) <= v14->m_nMaxOverlapNum )
      {
        TInvenSlot<_INVENKEY>::set_overlapnum(v16, n);
        TInvenSlot<_INVENKEY>::clear(v15);
      }
      else
      {
        TInvenSlot<_INVENKEY>::set_overlapnum(v16, v14->m_nMaxOverlapNum);
        TInvenSlot<_INVENKEY>::set_overlapnum(v15, n - v14->m_nMaxOverlapNum);
      }
    }
  }
  else
  {
    TInvenSlot<_INVENKEY>::TInvenSlot<_INVENKEY>(&v11, v15);
    TInvenSlot<_INVENKEY>::operator=(v15, v16);
    TInvenSlot<_INVENKEY>::operator=(v16, &v11);
    TInvenSlot<_INVENKEY>::~TInvenSlot<_INVENKEY>(&v11);
  }
}
