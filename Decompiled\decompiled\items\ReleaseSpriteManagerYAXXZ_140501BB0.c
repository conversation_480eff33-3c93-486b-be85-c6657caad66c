/*
 * Function: ?ReleaseSpriteManager@@YAXXZ
 * Address: 0x140501BB0
 */

void ReleaseSpriteManager(void)
{
  char *v0; // rcx@1
  int v1; // ebx@2
  __int64 v2; // rdi@3

  v0 = (char *)qword_184A79D78;
  if ( qword_184A79D78 )
  {
    v1 = 0;
    if ( dword_184A79D80 > 0 )
    {
      v2 = 0i64;
      while ( 1 )
      {
        CSprite::ReleaseTexMemSprite(*(CSprite **)&v0[v2 + 128]);
        ++v1;
        v2 += 136i64;
        if ( v1 >= dword_184A79D80 )
          break;
        v0 = (char *)qword_184A79D78;
      }
      v0 = (char *)qword_184A79D78;
    }
    if ( v0 )
    {
      Dfree(v0);
      qword_184A79D78 = 0i64;
    }
  }
}
