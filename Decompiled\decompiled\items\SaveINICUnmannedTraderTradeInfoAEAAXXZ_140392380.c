/*
 * Function: ?Save<PERSON><PERSON>@CUnmannedTraderTradeInfo@@AEAAXXZ
 * Address: 0x140392380
 */

void __usercall CUnmannedTraderTradeInfo::SaveINI(CUnmannedTraderTradeInfo *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp-30h] [bp-1458h]@1
  char DstBuf; // [sp+0h] [bp-1428h]@5
  char v7; // [sp+1h] [bp-1427h]@5
  unsigned __int64 v8; // [sp+1410h] [bp-18h]@4
  CUnmannedTraderTradeInfo *v9; // [sp+1430h] [bp+8h]@1

  v9 = this;
  v2 = alloca(a2);
  v3 = &v5;
  for ( i = 1300i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v9->m_bNeedUpdateSave )
  {
    DstBuf = 0;
    memset(&v7, 0, 0x13FFui64);
    _ui64toa_s(v9->m_ui64TotalOldIncome, &DstBuf, 0x1400ui64, 10);
    WritePrivateProfileStringA(
      CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADEINFO_SAVE_INI_SECTION_NAME,
      "OldIncome",
      &DstBuf,
      CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADEINFO_SAVE_INI_FILE_NAME);
    _ui64toa_s(v9->m_ui64TotalCurrentIncome, &DstBuf, 0x1400ui64, 10);
    WritePrivateProfileStringA(
      CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADEINFO_SAVE_INI_SECTION_NAME,
      "CurrentIncome",
      &DstBuf,
      CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADEINFO_SAVE_INI_FILE_NAME);
    v9->m_bNeedUpdateSave = 0;
  }
}
