/*
 * Function: ?post_receive@CMgrAvatorItemHistory@@QEAAXPEAVCPostData@@PEAD@Z
 * Address: 0x14023F120
 */

void __fastcall CMgrAvatorItemHistory::post_receive(CMgrAvatorItemHistory *this, CPostData *pPost, char *pFileName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@5
  __int64 v6; // [sp+0h] [bp-98h]@1
  char *v7; // [sp+20h] [bp-78h]@5
  char *v8; // [sp+28h] [bp-70h]@5
  char *v9; // [sp+30h] [bp-68h]@5
  char *v10; // [sp+38h] [bp-60h]@7
  char *v11; // [sp+40h] [bp-58h]@7
  char szTran[2]; // [sp+58h] [bp-40h]@4
  char v13; // [sp+5Ah] [bp-3Eh]@4
  _base_fld *v14; // [sp+78h] [bp-20h]@5
  unsigned __int64 v15; // [sp+88h] [bp-10h]@4
  CMgrAvatorItemHistory *v16; // [sp+A0h] [bp+8h]@1
  CPostData *v17; // [sp+A8h] [bp+10h]@1
  char *pszFileName; // [sp+B0h] [bp+18h]@1

  pszFileName = pFileName;
  v17 = pPost;
  v16 = this;
  v3 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v6 ^ _security_cookie;
  *(_WORD *)szTran = 0;
  memset(&v13, 0, 0xFui64);
  sData[0] = 0;
  W2M(pPost->m_wszSendName, szTran, 0x11u);
  if ( _INVENKEY::IsFilled(&v17->m_Key) )
  {
    v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v17->m_Key.byTableCode, v17->m_Key.wItemIndex);
    v5 = DisplayItemUpgInfo(v17->m_Key.byTableCode, v17->m_dwUpt);
    v9 = (char *)v17->m_lnUID;
    v8 = v5;
    v7 = (char *)v17->m_dwDur;
    sprintf_s(sBuf, 0x2800ui64, "%s_%I64u_@%s[%I64u]", v14->m_strCode);
  }
  else
  {
    sprintf_s(sBuf, 0x2800ui64, "NoItem");
  }
  v11 = v16->m_szCurTime;
  v10 = v16->m_szCurDate;
  v9 = szTran;
  LODWORD(v8) = v17->m_dwGold;
  v7 = sBuf;
  sprintf_s(
    sData,
    0x4E20ui64,
    "[PostSystem : Post Receive] - PostSerial[%u] - Item[%s] - Gold[%u] - Sender[%s] - [%s %s]\r\n",
    v17->m_dwPSSerial);
  CMgrAvatorItemHistory::WriteFile(v16, pszFileName, sData);
}
