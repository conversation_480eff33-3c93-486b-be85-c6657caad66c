/*
 * Function: ?Request@CMoveMapLimitInfoPortal@@UEAAEHHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A4200
 */

char __fastcall CMoveMapLimitInfoPortal::Request(CMoveMapLimitInfoPortal *this, int iUserInx, int iRequetType, char *pRequest, CMoveMapLimitRightInfo *pkRight)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@4
  CMoveMapLimitInfoPortal *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = iRequetType;
  if ( iRequetType )
  {
    if ( v9 == 1 )
    {
      result = CMoveMapLimitInfoPortal::ProcUseMoveScroll(v10, iUserInx, pRequest, pkRight);
    }
    else if ( v9 == 2 )
    {
      result = CMoveMapLimitInfoPortal::ProcGotoLimitZone(v10, iUserInx, pRequest, pkRight);
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = CMoveMapLimitInfoPortal::ProcForceMoveHQ(v10, iUserInx, pRequest, pkRight);
  }
  return result;
}
