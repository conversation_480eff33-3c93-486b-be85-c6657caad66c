/*
 * Function: ?SetFlag@CMoveMapLimitRightInfo@@QEAAXHH_N@Z
 * Address: 0x1403ACC50
 */

void __fastcall CMoveMapLimitRightInfo::SetFlag(CMoveMapLimitRightInfo *this, int iType, int iSubType, bool bFlag)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  CMoveMapLimitRight *v7; // [sp+20h] [bp-18h]@9
  CMoveMapLimitRightInfo *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1
  int v10; // [sp+50h] [bp+18h]@1
  bool v11; // [sp+58h] [bp+20h]@1

  v11 = bFlag;
  v10 = iSubType;
  v9 = iType;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( iType >= 0
    && !std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::empty(&v8->m_vecRight)
    && std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::size(&v8->m_vecRight) > v9 )
  {
    if ( *std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator[](&v8->m_vecRight, v9) )
    {
      v7 = *std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator[](&v8->m_vecRight, v9);
      ((void (__fastcall *)(CMoveMapLimitRight *, _QWORD, _QWORD))v7->vfptr->SetFlag)(v7, (unsigned int)v10, v11);
    }
  }
}
