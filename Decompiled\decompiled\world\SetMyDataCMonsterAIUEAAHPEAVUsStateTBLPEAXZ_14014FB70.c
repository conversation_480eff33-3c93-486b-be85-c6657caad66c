/*
 * Function: ?SetMyData@CMonsterAI@@UEAAHPEAVUsStateTBL@@PEAX@Z
 * Address: 0x14014FB70
 */

__int64 __fastcall CMonsterAI::SetMyData(CMonsterAI *this, UsStateTBL *pStateTBL, void *pObject)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMonsterAI *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return Us_HFSM::SetMyData((Us_HFSM *)&v7->vfptr, pStateTBL, pObject) != 0;
}
