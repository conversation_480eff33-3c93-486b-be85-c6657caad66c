/*
 * Function: ??$_Fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403AC570
 */

void __fastcall std::_Fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned __int64)*_Val, _Count);
}
