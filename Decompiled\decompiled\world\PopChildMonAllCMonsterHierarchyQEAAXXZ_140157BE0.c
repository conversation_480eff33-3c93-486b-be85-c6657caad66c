/*
 * Function: ?PopChildMonAll@CMonsterHierarchy@@QEAAXXZ
 * Address: 0x140157BE0
 */

void __fastcall CMonsterHierarchy::PopChildMonAll(CMonsterHierarchy *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  unsigned int k; // [sp+24h] [bp-14h]@6
  CMonsterHierarchy *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    for ( k = 0; k < 0xA; ++k )
    {
      if ( v6->m_pChildMon[j][k] )
      {
        CMonsterHierarchy::SetParent(&v6->m_pChildMon[j][k]->m_MonHierarcy, 0i64);
        v6->m_pChildMon[j][k] = 0i64;
        --v6->m_dwMonCount[j];
        --v6->m_dwTotalCount;
      }
    }
  }
}
