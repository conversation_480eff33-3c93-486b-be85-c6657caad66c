/*
 * Function: ?Search@CUnmannedTraderUserInfo@@QEAAXEPEAU_unmannedtrader_search_list_request_cl<PERSON>@@PEAVCLogFile@@@Z
 * Address: 0x1403540D0
 */

void __fastcall CUnmannedTraderUserInfo::Search(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_search_list_request_clzo *pRequest, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderGroupItemInfoTable *v6; // rax@7
  CLogTypeDBTaskManager *v7; // rax@9
  __int64 v8; // [sp+0h] [bp-438h]@1
  unsigned int dwListIndex; // [sp+34h] [bp-404h]@5
  unsigned int v10; // [sp+54h] [bp-3E4h]@5
  char v11; // [sp+64h] [bp-3D4h]@5
  CUnmannedTraderSortType *v12; // [sp+68h] [bp-3D0h]@7
  char *Source; // [sp+70h] [bp-3C8h]@7
  char pcData[2]; // [sp+90h] [bp-3A8h]@7
  unsigned int v15; // [sp+94h] [bp-3A4h]@7
  char v16; // [sp+98h] [bp-3A0h]@7
  char v17; // [sp+99h] [bp-39Fh]@7
  unsigned int v18; // [sp+9Ch] [bp-39Ch]@7
  char v19; // [sp+A0h] [bp-398h]@7
  char v20; // [sp+A1h] [bp-397h]@7
  char v21; // [sp+A2h] [bp-396h]@7
  char v22; // [sp+A3h] [bp-395h]@7
  unsigned int v23; // [sp+A4h] [bp-394h]@7
  char v24; // [sp+A8h] [bp-390h]@7
  char Dest; // [sp+A9h] [bp-38Fh]@7
  unsigned __int64 v26; // [sp+420h] [bp-18h]@4
  CUnmannedTraderUserInfo *v27; // [sp+440h] [bp+8h]@1
  char v28; // [sp+448h] [bp+10h]@1
  _unmannedtrader_search_list_request_clzo *pRequesta; // [sp+450h] [bp+18h]@1

  pRequesta = pRequest;
  v28 = byType;
  v27 = this;
  v4 = &v8;
  for ( i = 268i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v26 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byType < 2 )
  {
    dwListIndex = 0;
    v10 = 0;
    v11 = CUnmannedTraderUserInfo::CheckSearch(v27, byType, pRequest, &dwListIndex, &v10, pkLogger);
    if ( v11 )
    {
      CUnmannedTraderUserInfo::SendSearchErrorResult(v27, v27->m_wInx, v11);
    }
    else
    {
      v6 = CUnmannedTraderGroupItemInfoTable::Instance();
      v12 = CUnmannedTraderGroupItemInfoTable::GetSortType(v6, pRequesta->byDivision, pRequesta->bySortType);
      Source = (char *)CUnmannedTraderSortType::GetQuery(v12);
      *(_WORD *)pcData = v27->m_wInx;
      v15 = v27->m_dwUserSerial;
      v16 = v28;
      v17 = CPlayerDB::GetRaceCode((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v27->m_wInx));
      v18 = dwListIndex;
      v19 = pRequesta->byDivision;
      v20 = pRequesta->byClass;
      v21 = pRequesta->bySubClass;
      v22 = pRequesta->bySortType;
      v23 = v10;
      v24 = pRequesta->byPage;
      Dest = 0;
      if ( Source )
        strcpy_0(&Dest, Source);
      CUnmannedTraderRequestLimiter::SetRequest(&v27->m_kRequestState, 4);
      v7 = CLogTypeDBTaskManager::Instance();
      CLogTypeDBTaskManager::Push(v7, 1, pcData, 0x378u);
    }
  }
}
