/*
 * Function: ?SelectBuy@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034D520
 */

char __fastcall CUnmannedTraderController::SelectBuy(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-108h]@1
  char *byProcRet; // [sp+20h] [bp-E8h]@7
  char *v7; // [sp+30h] [bp-D8h]@4
  int v8; // [sp+38h] [bp-D0h]@4
  int v9; // [sp+3Ch] [bp-CCh]@4
  unsigned int pSalesTotals; // [sp+44h] [bp-C4h]@4
  unsigned __int64 v11; // [sp+58h] [bp-B0h]@4
  char v12; // [sp+60h] [bp-A8h]@4
  char v13; // [sp+61h] [bp-A7h]@4
  char byState; // [sp+74h] [bp-94h]@4
  int Dst; // [sp+98h] [bp-70h]@4
  unsigned int dwOwnor; // [sp+9Ch] [bp-6Ch]@14
  char v17; // [sp+A0h] [bp-68h]@18
  int v18; // [sp+A4h] [bp-64h]@18
  int v19; // [sp+A8h] [bp-60h]@20
  int v20; // [sp+ACh] [bp-5Ch]@20
  char Src; // [sp+B0h] [bp-58h]@20
  char v22; // [sp+BDh] [bp-4Bh]@20
  int j; // [sp+E4h] [bp-24h]@4
  unsigned __int64 v24; // [sp+F0h] [bp-18h]@4
  CUnmannedTraderController *v25; // [sp+110h] [bp+8h]@1

  v25 = this;
  v2 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v24 = (unsigned __int64)&v5 ^ _security_cookie;
  v7 = pData;
  v8 = 0;
  v9 = 0;
  pSalesTotals = 0;
  v11 = 0i64;
  v12 = 0;
  v13 = 0;
  byState = -1;
  memset_0(&Dst, 0, 0x3Cui64);
  for ( j = 0; j < (unsigned __int8)v7[14]; ++j )
  {
    v7[72 * j + 84] = 0;
    byProcRet = &v7[72 * j + 84];
    v13 = CUnmannedTraderController::CheckDBItemState(v25, v7[13], *(_DWORD *)&v7[72 * j + 16], &byState, byProcRet);
    if ( !v7[72 * j + 84] )
    {
      byProcRet = (char *)&Dst;
      v13 = CRFWorldDatabase::Select_UnmannedTraderSellInfo(
              pkDB,
              v7[13],
              *(_DWORD *)&v7[72 * j + 16],
              v7[8],
              (_unmannedtrader_seller_info *)&Dst);
      if ( v13 == 1 )
      {
        v7[72 * j + 84] = 31;
      }
      else if ( v13 == 2 )
      {
        v7[72 * j + 84] = 32;
      }
      else if ( Dst == *(_DWORD *)&v7[72 * j + 20] )
      {
        if ( dwOwnor == *((_DWORD *)v7 + 1) )
        {
          v7[72 * j + 84] = 39;
        }
        else
        {
          pSalesTotals = 0;
          v11 = 0i64;
          v13 = CRFWorldDatabase::Select_utSellWaitItems_SalesTotals(pkDB, v7[13], dwOwnor, &pSalesTotals);
          if ( v13 == 1 )
          {
            v7[72 * j + 84] = 31;
          }
          else
          {
            *(_DWORD *)&v7[72 * j + 20] = Dst;
            *(_DWORD *)&v7[72 * j + 24] = dwOwnor;
            v7[72 * j + 28] = v17;
            v11 = pSalesTotals + v18;
            if ( v11 > 0x77359400 )
              v11 = 2000000000i64;
            *(_DWORD *)&v7[72 * j + 32] = v11;
            *(_DWORD *)&v7[72 * j + 36] = v19;
            *(_DWORD *)&v7[72 * j + 44] = v20;
            strcpy_s(&v7[72 * j + 48], 0xDui64, &Src);
            strcpy_s(&v7[72 * j + 61], 0x11ui64, &v22);
            v7[72 * j + 40] = 0;
            if ( CUnmannedTraderEnvironmentValue::Unmanned_Trader_Dev_Account_Start_Serial <= *(_DWORD *)&v7[72 * j + 44] )
              v7[72 * j + 40] = 3;
          }
        }
      }
      else
      {
        v7[72 * j + 84] = 44;
      }
    }
  }
  return 0;
}
