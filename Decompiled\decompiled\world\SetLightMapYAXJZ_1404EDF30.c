/*
 * Function: ?SetLightMap@@YAXJ@Z
 * Address: 0x1404EDF30
 */

void __fastcall SetLightMap(int a1)
{
  int v1; // esi@1
  struct IDirect3DDevice8 *v2; // rax@1
  struct IDirect3DDevice8 *v3; // rdi@1
  IUnknownVtbl *v4; // rbx@3
  void *v5; // rax@3

  v1 = a1;
  v2 = GetD3dDevice();
  v3 = v2;
  if ( v1 == -1 )
  {
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, _QWORD))v2->vfptr[20].AddRef)(v2, 1i64, 0i64);
  }
  else
  {
    v4 = v2->vfptr;
    v5 = GetLightMapSurface(v1);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, void *))v4[20].AddRef)(v3, 1i64, v5);
  }
}
