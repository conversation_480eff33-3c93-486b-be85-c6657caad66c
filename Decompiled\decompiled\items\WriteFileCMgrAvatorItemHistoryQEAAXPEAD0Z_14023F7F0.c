/*
 * Function: ?WriteFile@CMgrAvatorItemHistory@@QEAAXPEAD0@Z
 * Address: 0x14023F7F0
 */

void __fastcall CMgrAvatorItemHistory::WriteFile(CMgrAvatorItemHistory *this, char *pszFileName, char *pszLog)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-34h]@5
  size_t Size; // [sp+34h] [bp-24h]@4
  char *Dest; // [sp+40h] [bp-18h]@10
  char *v9; // [sp+48h] [bp-10h]@14
  CMgrAvatorItemHistory *v10; // [sp+60h] [bp+8h]@1
  const char *Source; // [sp+68h] [bp+10h]@1
  char *Str; // [sp+70h] [bp+18h]@1

  Str = pszLog;
  Source = pszFileName;
  v10 = this;
  v3 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  LODWORD(Size) = strlen_0(pszLog);
  if ( (unsigned int)Size >= 0xC8 )
  {
    if ( (unsigned int)Size >= 0x3E8 )
    {
      if ( (unsigned int)Size < 0x2710 && CNetIndexList::PopNode_Front(&v10->m_listLogDataEmpty_10K, &pdwOutIndex) )
      {
        v9 = v10->m_LogData_10K[pdwOutIndex].szFileName;
        strcpy_0(v9, Source);
        *((_DWORD *)v9 + 16) = Size;
        memcpy_0(v9 + 68, Str, (unsigned int)Size);
        v9[(unsigned int)Size + 68] = 0;
        CNetIndexList::PushNode_Back(&v10->m_listLogData_10K, pdwOutIndex);
        return;
      }
    }
    else if ( CNetIndexList::PopNode_Front(&v10->m_listLogDataEmpty_1K, &pdwOutIndex) )
    {
      Dest = v10->m_LogData_1K[pdwOutIndex].szFileName;
      strcpy_0(Dest, Source);
      *((_DWORD *)Dest + 16) = Size;
      memcpy_0(Dest + 68, Str, (unsigned int)Size);
      Dest[(unsigned int)Size + 68] = 0;
      CNetIndexList::PushNode_Back(&v10->m_listLogData_1K, pdwOutIndex);
      return;
    }
  }
  else if ( CNetIndexList::PopNode_Front(&v10->m_listLogDataEmpty_200, &pdwOutIndex) )
  {
    *(size_t *)((char *)&Size + 4) = (size_t)&v10->m_LogData_200[pdwOutIndex];
    strcpy_0(*(char **)((char *)&Size + 4), Source);
    *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 64) = Size;
    memcpy_0((void *)(*(size_t *)((char *)&Size + 4) + 68), Str, (unsigned int)Size);
    *(_BYTE *)(*(size_t *)((char *)&Size + 4) + (unsigned int)Size + 68) = 0;
    CNetIndexList::PushNode_Back(&v10->m_listLogData_200, pdwOutIndex);
    return;
  }
  IOFileWrite_0((char *)Source, Size, Str);
}
