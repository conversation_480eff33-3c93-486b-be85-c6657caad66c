/*
 * Function: ?_LoadStoreDummy@CMapData@@AEAA_NPEAD@Z
 * Address: 0x140182A10
 */

char __fastcall CMapData::_LoadStoreDummy(CMapData *this, char *pszMapCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v5; // eax@9
  int v6; // eax@20
  __int64 v7; // [sp+0h] [bp-148h]@1
  char Dest; // [sp+30h] [bp-118h]@4
  CRecordData *v9; // [sp+B8h] [bp-90h]@8
  int n; // [sp+C0h] [bp-88h]@8
  _base_fld *v11; // [sp+C8h] [bp-80h]@10
  int v12; // [sp+D0h] [bp-78h]@19
  _base_fld *pRec; // [sp+D8h] [bp-70h]@21
  _dummy_position *pDumPos; // [sp+E0h] [bp-68h]@22
  _store_dummy *v15; // [sp+E8h] [bp-60h]@24
  int j; // [sp+F0h] [bp-58h]@26
  int __n[2]; // [sp+100h] [bp-48h]@16
  void *v18; // [sp+108h] [bp-40h]@19
  void *__t; // [sp+110h] [bp-38h]@16
  __int64 v20; // [sp+118h] [bp-30h]@4
  void *v21; // [sp+120h] [bp-28h]@17
  CDummyPosTable *v22; // [sp+128h] [bp-20h]@29
  unsigned __int64 v23; // [sp+130h] [bp-18h]@4
  CMapData *v24; // [sp+150h] [bp+8h]@1
  const char *Str2; // [sp+158h] [bp+10h]@1

  Str2 = pszMapCode;
  v24 = this;
  v2 = &v7;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v20 = -2i64;
  v23 = (unsigned __int64)&v7 ^ _security_cookie;
  sprintf(&Dest, ".\\map\\%s\\%s.spt", pszMapCode, pszMapCode);
  if ( CDummyPosTable::LoadDummyPosition(&v24->m_tbStoreDumPos, &Dest, "*sd") )
  {
    if ( CMapData::ConvertLocalToWorldDummy(v24, &v24->m_tbStoreDumPos, 0) )
    {
      v9 = &CItemStoreManager::Instance()->m_tblItemStore;
      for ( n = 0; ; ++n )
      {
        v5 = CRecordData::GetRecordNum(v9);
        if ( n >= v5 )
          break;
        v11 = CRecordData::GetRecord(v9, n);
        if ( !strcmp_0(&v11[3].m_strCode[52], Str2) )
          ++v24->m_nItemStoreDumNum;
      }
      if ( CDummyPosTable::GetRecordNum(&v24->m_tbStoreDumPos) )
      {
        if ( v24->m_nItemStoreDumNum > 0 )
        {
          *(_QWORD *)__n = v24->m_nItemStoreDumNum;
          __t = operator new[](saturated_mul(0x18ui64, *(unsigned __int64 *)__n));
          if ( __t )
          {
            `vector constructor iterator'(__t, 0x18ui64, __n[0], (void *(__cdecl *)(void *))_store_dummy::_store_dummy);
            v21 = __t;
          }
          else
          {
            v21 = 0i64;
          }
          v18 = v21;
          v24->m_pItemStoreDummy = (_store_dummy *)v21;
          v12 = 0;
          for ( n = 0; ; ++n )
          {
            v6 = CRecordData::GetRecordNum(v9);
            if ( n >= v6 )
              break;
            pRec = CRecordData::GetRecord(v9, n);
            if ( !strcmp_0(&pRec[3].m_strCode[52], Str2) )
            {
              pDumPos = CDummyPosTable::GetRecord(&v24->m_tbStoreDumPos, pRec->m_strCode);
              if ( !pDumPos )
              {
                MyMessageBox(
                  "CMapData-LoadStore(LPTSTR pszMapCode) Error",
                  "Map(%s): m_tbStoreDumPos.GetRecord(%s) = NULL",
                  Str2,
                  pRec->m_strCode);
                return 0;
              }
              v15 = &v24->m_pItemStoreDummy[v12];
              _store_dummy::SetDummy(v15, 0, pRec, pDumPos);
              CMapData::CheckCenterPosDummy(v24, pDumPos);
              ++v12;
            }
          }
        }
        for ( j = 0; j < 3; ++j )
        {
          if ( !CDummyPosTable::ms_pHeroes_Dummy[j] )
          {
            v22 = &v24->m_tbStoreDumPos;
            CDummyPosTable::ms_pHeroes_Dummy[j] = CDummyPosTable::GetRecord(
                                                    &v24->m_tbStoreDumPos,
                                                    C_HEROES_DUMMY_NAME[j]);
          }
        }
        result = 1;
      }
      else
      {
        v24->m_nItemStoreDumNum = 0;
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox("CMapData Error", "m_tbStoreDumPos.LoadDummyPosition(%s) == false", &Dest);
    result = 0;
  }
  return result;
}
