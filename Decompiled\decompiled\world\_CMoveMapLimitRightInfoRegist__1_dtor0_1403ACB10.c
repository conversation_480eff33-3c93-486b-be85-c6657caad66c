/*
 * Function: _CMoveMapLimitRightInfo::Regist_::_1_::dtor$0
 * Address: 0x1403ACB10
 */

void __fastcall CMoveMapLimitRightInfo::Regist_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 128));
}
