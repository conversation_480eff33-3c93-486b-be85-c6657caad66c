/*
 * Function: ??$def@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAXPEAUlua_State@@PEBDP6APEAVCMonster@@PEAD2MMM@Z@Z
 * Address: 0x140407ED0
 */

void __fastcall lua_tinker::def<CMonster * (*)(char *,char *,float,float,float)>(struct lua_State *L, const char *name, CMonster *(__cdecl *func)(char *, char *, float, float, float))
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  struct lua_State *La; // [sp+30h] [bp+8h]@1
  CMonster *(__cdecl *funca)(char *, char *, float, float, float); // [sp+40h] [bp+18h]@1

  funca = func;
  La = L;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  lua_pushstring(La, name);
  lua_pushlightuserdata(La, funca);
  lua_tinker::push_functor<CMonster *,char *,char *,float,float,float>(La, funca);
  lua_settable(La, 4294957294i64);
}
