/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::catch$1
 * Address: 0x1403B1340
 */

void __fastcall __noreturn std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Destroy(
    *(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > **)(a2 + 192),
    (CMoveMapLimitRightInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 200) + 16i64) + 40i64 * *(_QWORD *)(a2 + 208)),
    (CMoveMapLimitRightInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 192) + 24i64) + 40i64 * *(_QWORD *)(a2 + 208)));
  CxxThrowException_0(0i64, 0i64);
}
