/*
 * Function: ?Push@CIndexListEx@?$ListHeap@UCell@LendItemSheet@@@@QEAA_NPEAU_index_node@CNetIndexList@@K@Z
 * Address: 0x1403116B0
 */

char __fastcall ListHeap<LendItemSheet::Cell>::CIndexListEx::Push(ListHeap<LendItemSheet::Cell>::CIndexListEx *this, CNetIndexList::_index_node *pos, unsigned int dwIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  CNetIndexList::_index_node *v7; // [sp+20h] [bp-18h]@7
  ListHeap<LendItemSheet::Cell>::CIndexListEx *v8; // [sp+40h] [bp+8h]@1
  CNetIndexList::_index_node *v9; // [sp+48h] [bp+10h]@1
  unsigned int v10; // [sp+50h] [bp+18h]@1

  v10 = dwIndex;
  v9 = pos;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CNetCriticalSection::Lock(&v8->m_csList);
  if ( v9 && v8->m_BufHead.m_pNext != &v8->m_BufTail )
  {
    v7 = v8->m_BufHead.m_pNext;
    v8->m_BufHead.m_pNext = v7->m_pNext;
    v7->m_pNext->m_pPrev = &v8->m_BufHead;
    --v8->m_dwBufCount;
    v7->m_dwIndex = v10;
    v7->m_pNext = v9;
    v7->m_pPrev = v9->m_pPrev;
    v9->m_pPrev->m_pNext = v7;
    v9->m_pPrev = v7;
    ++v8->m_dwCount;
    CNetCriticalSection::Unlock(&v8->m_csList);
    result = 1;
  }
  else
  {
    CNetCriticalSection::Unlock(&v8->m_csList);
    result = 0;
  }
  return result;
}
