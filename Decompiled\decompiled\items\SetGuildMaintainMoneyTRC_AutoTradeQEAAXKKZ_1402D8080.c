/*
 * Function: ?SetGuild<PERSON>ain<PERSON>Money@TRC_AutoTrade@@QEAAXKK@Z
 * Address: 0x1402D8080
 */

void __fastcall TRC_AutoTrade::SetGuildMaintainMoney(TRC_AutoTrade *this, unsigned int dwTax, unsigned int dwSeller)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  TRC_AutoTrade *v6; // [sp+30h] [bp+8h]@1
  signed int dwRetPrice; // [sp+38h] [bp+10h]@1

  dwRetPrice = dwTax;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v6->m_pOwnerGuild )
    TRC_AutoTrade::PushDQSData_GuildInMoney(v6, dwTax, dwSeller);
  ++v6->m_dwTrade;
  v6->m_dIncomeMoney = v6->m_dIncomeMoney + (double)dwRetPrice;
}
