/*
 * Function: ??$_Destroy@VCMoveMapLimitRightInfo@@@std@@YAXPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A38D0
 */

void __fastcall std::_Destroy<CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_Ptr)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = _Ptr;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CMoveMapLimitRightInfo::`scalar deleting destructor'(v4, 0);
}
