/*
 * Function: ?OnDisplay@CMapDisplay@@QEAA_NPEAVCMapData@@G@Z
 * Address: 0x14019EEB0
 */

char __fastcall CMapDisplay::OnDisplay(CMapDisplay *this, CMapData *pMap, unsigned __int16 wLayerIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@6
  CMapDisplay *v8; // [sp+40h] [bp+8h]@1
  CMapData *pMapa; // [sp+48h] [bp+10h]@1
  unsigned __int16 v10; // [sp+50h] [bp+18h]@1

  v10 = wLayerIndex;
  pMapa = pMap;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v8->m_bDisplayMode )
  {
    result = 0;
  }
  else
  {
    v8->m_bDisplayMode = 1;
    v7 = CMapDisplay::InitSurface(v8, pMap);
    if ( v7 )
    {
      result = 0;
    }
    else
    {
      v8->m_pActMap = pMapa;
      v8->m_wLayerIndex = 0;
      if ( v10 >= pMapa->m_pMapSet->m_nLayerNum )
        v8->m_wLayerIndex = 0;
      result = 1;
    }
  }
  return result;
}
