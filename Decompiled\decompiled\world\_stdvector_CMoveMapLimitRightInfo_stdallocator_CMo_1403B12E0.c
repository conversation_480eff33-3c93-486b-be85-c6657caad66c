/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::catch$0
 * Address: 0x1403B12E0
 */

void __fastcall __noreturn std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Destroy(
    *(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > **)(a2 + 192),
    *(CMoveMapLimitRightInfo **)(a2 + 96),
    *(CMoveMapLimitRightInfo **)(a2 + 104));
  std::allocator<CMoveMapLimitRightInfo>::deallocate(
    (std::allocator<CMoveMapLimitRightInfo> *)(*(_QWORD *)(v2 + 192) + 8i64),
    *(CMoveMapLimitRightInfo **)(v2 + 96),
    *(_QWORD *)(v2 + 88));
  CxxThrowException_0(0i64, 0i64);
}
