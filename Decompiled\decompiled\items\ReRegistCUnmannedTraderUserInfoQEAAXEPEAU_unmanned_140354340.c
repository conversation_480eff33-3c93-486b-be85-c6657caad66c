/*
 * Function: ?ReRegist@CUnmannedTraderUserInfo@@QEAAXEPEAU_unmannedtrader_re_regist_request_cl<PERSON>@@PEAVCLogFile@@@Z
 * Address: 0x140354340
 */

void __fastcall CUnmannedTraderUserInfo::ReRegist(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_re_regist_request_clzo *pRequest, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CMoneySupplyMgr *v6; // rax@18
  int v7; // eax@19
  __int64 v8; // [sp+0h] [bp-208h]@1
  char byAmount[8]; // [sp+20h] [bp-1E8h]@10
  char byItemTableCode[4]; // [sp+28h] [bp-1E0h]@10
  unsigned __int16 wItemIndex; // [sp+30h] [bp-1D8h]@10
  unsigned int dwRegistSerial; // [sp+38h] [bp-1D0h]@10
  unsigned int dwPrice; // [sp+40h] [bp-1C8h]@10
  char *pbyDivision; // [sp+48h] [bp-1C0h]@10
  char *pbyClass; // [sp+50h] [bp-1B8h]@10
  char *pbySubClass; // [sp+58h] [bp-1B0h]@10
  unsigned int *pdwTax; // [sp+60h] [bp-1A8h]@10
  unsigned int *pdwListIndex; // [sp+68h] [bp-1A0h]@10
  unsigned int dwSub; // [sp+70h] [bp-198h]@7
  _qry_case_unmandtrader_re_registsingleitem Dst; // [sp+90h] [bp-178h]@7
  int j; // [sp+1C4h] [bp-44h]@7
  int v22; // [sp+1C8h] [bp-40h]@14
  int nLv; // [sp+1CCh] [bp-3Ch]@18
  int v24; // [sp+1D0h] [bp-38h]@18
  CUnmannedTraderUserInfo *v25; // [sp+210h] [bp+8h]@1
  char v26; // [sp+218h] [bp+10h]@1
  _unmannedtrader_re_regist_request_clzo *v27; // [sp+220h] [bp+18h]@1
  CLogFile *pkLoggera; // [sp+228h] [bp+20h]@1

  pkLoggera = pkLogger;
  v27 = pRequest;
  v26 = byType;
  v25 = this;
  v4 = &v8;
  for ( i = 122i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byType < 2 && pRequest->byRegedNum )
  {
    CUnmannedTraderUserInfo::CountRegistItem(v25);
    dwSub = 0;
    _qry_case_unmandtrader_re_registsingleitem::_qry_case_unmandtrader_re_registsingleitem(&Dst);
    memset_0(&Dst, 0, 0x124ui64);
    Dst.byType = 0;
    Dst.byNum = v27->byRegedNum;
    Dst.wInx = v25->m_wInx;
    Dst.dwOwnerSerial = v25->m_dwUserSerial;
    for ( j = 0; j < v27->byRegedNum; ++j )
    {
      Dst.List[j].bRegist = v27->List[j].bRegist;
      Dst.List[j].wItemSerial = v27->List[j].wItemSerial;
      Dst.List[j].dwPrice = v27->List[j].dwPrice;
      Dst.List[j].dwRegistSerial = v27->List[j].dwRegistSerial;
      Dst.List[j].byUpdateState = 11;
      if ( Dst.List[j].bRegist )
      {
        pdwListIndex = &Dst.List[j].dwListIndex;
        pdwTax = &Dst.List[j].dwTax;
        pbySubClass = &Dst.List[j].byClass3;
        pbyClass = &Dst.List[j].byClass2;
        pbyDivision = &Dst.List[j].byClass1;
        dwPrice = v27->List[j].dwPrice;
        dwRegistSerial = v27->List[j].dwRegistSerial;
        wItemIndex = v27->List[j].wItemIndex;
        byItemTableCode[0] = v27->List[j].byItemTableCode;
        byAmount[0] = v27->List[j].byAmount;
        Dst.List[j].byProcRet = CUnmannedTraderUserInfo::CheckReRegist(
                                  v25,
                                  v26,
                                  pkLoggera,
                                  v27->List[j].wItemSerial,
                                  byAmount[0],
                                  byItemTableCode[0],
                                  wItemIndex,
                                  dwRegistSerial,
                                  dwPrice,
                                  pbyDivision,
                                  pbyClass,
                                  pbySubClass,
                                  pdwTax,
                                  pdwListIndex);
        if ( !Dst.List[j].byProcRet )
        {
          Dst.List[j].byUpdateState = 1;
          dwSub += Dst.List[j].dwTax;
        }
      }
    }
    if ( dwSub )
    {
      CPlayer::SubDalant(&g_Player + v25->m_wInx, dwSub);
      v22 = CPlayerDB::GetLevel((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v25->m_wInx));
      if ( v22 == 30 || v22 == 40 || v22 == 50 || v22 == 60 )
      {
        nLv = CPlayerDB::GetLevel((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v25->m_wInx));
        v24 = CPlayerDB::GetRaceCode((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v25->m_wInx));
        v6 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v6, v24, nLv, dwSub);
      }
    }
    CUnmannedTraderRequestLimiter::SetRequest(&v25->m_kRequestState, 5);
    v7 = _qry_case_unmandtrader_re_registsingleitem::size(&Dst);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -116, &Dst.byType, v7);
  }
}
