/*
 * Function: ?SearchNearMonster@CMonsterHelper@@SAKPEAVCMonster@@PEAU_NEAR_DATA@@KH@Z
 * Address: 0x140158DE0
 */

__int64 __fastcall CMonsterHelper::SearchNearMonster(CMonster *pMon, _NEAR_DATA *NearChar, unsigned int dwArSize, int bTargetIgnore)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  int v7; // eax@13
  _sec_info *v8; // rax@18
  float v9; // xmm0_4@26
  int v10; // eax@28
  int v11; // eax@30
  float v12; // xmm0_4@32
  float v13; // xmm0_4@32
  float v14; // xmm0_4@33
  float v15; // xmm0_4@34
  float *v16; // rax@51
  int v17; // eax@51
  __int64 v18; // [sp+0h] [bp-108h]@1
  unsigned int j; // [sp+20h] [bp-E8h]@10
  __int64 *v20; // [sp+28h] [bp-E0h]@13
  _pnt_rect pRect; // [sp+38h] [bp-D0h]@13
  float v22; // [sp+54h] [bp-B4h]@13
  float v23[3]; // [sp+68h] [bp-A0h]@51
  int k; // [sp+84h] [bp-84h]@13
  int l; // [sp+88h] [bp-80h]@15
  unsigned int dwSecIndex; // [sp+8Ch] [bp-7Ch]@18
  CObjectList *v27; // [sp+90h] [bp-78h]@18
  _object_list_point *v28; // [sp+98h] [bp-70h]@19
  CMonster *v29; // [sp+A0h] [bp-68h]@21
  char *v30; // [sp+A8h] [bp-60h]@21
  int v31; // [sp+B0h] [bp-58h]@35
  unsigned int m; // [sp+B4h] [bp-54h]@38
  unsigned int v33; // [sp+B8h] [bp-50h]@41
  CCharacter *v34; // [sp+C0h] [bp-48h]@48
  unsigned int v35; // [sp+C8h] [bp-40h]@48
  unsigned int n; // [sp+CCh] [bp-3Ch]@48
  _monster_fld *v37; // [sp+D0h] [bp-38h]@28
  __int64 v38; // [sp+D8h] [bp-30h]@28
  int v39; // [sp+E0h] [bp-28h]@30
  float v40; // [sp+E4h] [bp-24h]@32
  float v41; // [sp+E8h] [bp-20h]@33
  void *Dst; // [sp+F0h] [bp-18h]@42
  CMapData *v43; // [sp+F8h] [bp-10h]@51
  CMonster *v44; // [sp+110h] [bp+8h]@1
  _NEAR_DATA *v45; // [sp+118h] [bp+10h]@1
  unsigned int v46; // [sp+120h] [bp+18h]@1
  int v47; // [sp+128h] [bp+20h]@1

  v47 = bTargetIgnore;
  v46 = dwArSize;
  v45 = NearChar;
  v44 = pMon;
  v4 = &v18;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v44->m_pMonRec->m_nRaceCode == 3 )
  {
    result = 0i64;
  }
  else if ( v44 && NearChar && dwArSize )
  {
    for ( j = 0; j < v46; ++j )
      _NEAR_DATA::Init(&v45[j]);
    v20 = (__int64 *)v44->m_pTargetChar;
    v22 = *(float *)&v44->m_pRecordSet[25].m_strCode[52];
    v7 = CGameObject::GetCurSecNum((CGameObject *)&v44->vfptr);
    CMapData::GetRectInRadius(v44->m_pCurMap, &pRect, 4, v7);
    for ( k = pRect.nStarty; k <= pRect.nEndy; ++k )
    {
      for ( l = pRect.nStartx; l <= pRect.nEndx; ++l )
      {
        v8 = CMapData::GetSecInfo(v44->m_pCurMap);
        dwSecIndex = v8->m_nSecNumW * k + l;
        v27 = CMapData::GetSectorListObj(v44->m_pCurMap, v44->m_wMapLayerIndex, dwSecIndex);
        if ( v27 )
        {
          v28 = v27->m_Head.m_pNext;
LABEL_20:
          while ( v28 != &v27->m_Tail )
          {
            v29 = (CMonster *)v28->m_pItem;
            v28 = v28->m_pNext;
            v30 = &v29->m_ObjID.m_byKind;
            if ( !v29->m_ObjID.m_byKind
              && v30[1] == 1
              && *((_WORD *)v30 + 1) != v44->m_ObjID.m_wIndex
              && (v47 || !v29->m_pTargetChar) )
            {
              v9 = v29->m_fCurPos[1] - v44->m_fCurPos[1];
              abs(v9);
              if ( v9 <= 50.0 )
              {
                if ( !v20
                  || (v37 = v29->m_pMonRec,
                      v38 = *v20,
                      v10 = (*(int (__fastcall **)(__int64 *))(v38 + 328))(v20),
                      v37->m_nRaceCode != v10) )
                {
                  if ( CMonster::GetMob_AsistType(v44)
                    || (v39 = CMonster::GetMob_SubRace(v44), v11 = CMonster::GetMob_SubRace(v29), v39 == v11)
                    || CMonster::GetMob_AsistType(v29) )
                  {
                    v12 = v29->m_fCurPos[0] - v44->m_fCurPos[0];
                    abs(v12);
                    v40 = v12;
                    v13 = v29->m_fCurPos[2] - v44->m_fCurPos[2];
                    abs(v13);
                    if ( v40 <= v13 )
                    {
                      v15 = v29->m_fCurPos[2] - v44->m_fCurPos[2];
                      abs(v15);
                      v41 = v15;
                    }
                    else
                    {
                      v14 = v29->m_fCurPos[0] - v44->m_fCurPos[0];
                      abs(v14);
                      v41 = v14;
                    }
                    v31 = (signed int)ffloor(v41);
                    if ( (float)v31 <= v22 )
                    {
                      if ( v45->pChar )
                      {
                        for ( m = 0; m < v46; ++m )
                        {
                          if ( v45[m].fLen > (float)v31 )
                          {
                            v33 = v46 - 1 - m;
                            if ( (signed int)v33 > 0 )
                            {
                              Dst = &v45[m + 1];
                              memcpy_0(Dst, &v45[m], 24i64 * (signed int)v33);
                            }
                            v45[m].fLen = (float)v31;
                            v45[m].pChar = (CCharacter *)v29;
                            goto LABEL_20;
                          }
                        }
                      }
                      else
                      {
                        v45->fLen = (float)v31;
                        v45->pChar = (CCharacter *)v29;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    v34 = 0i64;
    v35 = 0;
    for ( n = 0; n < v46 && v45[n].pChar; ++n )
    {
      v16 = v45[n].pChar->m_fCurPos;
      v43 = v44->m_pCurMap;
      v17 = CBsp::CanYouGoThere(v43->m_Level.mBsp, v44->m_fCurPos, v16, (float (*)[3])v23);
      v45[n].bCanYouGoThere = v17;
      if ( v45[n].bCanYouGoThere && !v34 )
        v34 = v45[n].pChar;
      ++v35;
    }
    if ( v34 )
      v44->m_LifeCicle = GetLoopTime();
    result = v35;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
