/*
 * Function: ?SetDummyRange@CDummyDraw@@QEAAXPEAVCMapData@@PEAM111HPEAVCRect@@@Z
 * Address: 0x14019C7B0
 */

void __fastcall CDummyDraw::SetDummyRange(CDummyDraw *this, CMapData *pMap, float *pLT, float *pRB, float *pRT, float *pLB, int nType, CRect *prcWnd)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-38h]@1
  _bsp_info *v11; // [sp+20h] [bp-18h]@4
  int j; // [sp+28h] [bp-10h]@4
  CDummyDraw *v13; // [sp+40h] [bp+8h]@1
  float *v14; // [sp+50h] [bp+18h]@1
  float *v15; // [sp+58h] [bp+20h]@1

  v15 = pRB;
  v14 = pLT;
  v13 = this;
  v8 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v13->m_pMap = pMap;
  v13->m_nType = nType;
  v11 = CMapData::GetBspInfo(pMap);
  v13->m_fPosAbs[0] = (float)(-0.0 - (float)v11->m_nMapMinSize[0]) + *v14;
  v13->m_fPosAbs[1] = (float)v11->m_nMapMaxSize[2] - v14[2];
  v13->m_fPosAbs[2] = (float)(-0.0 - (float)v11->m_nMapMinSize[0]) + *pRT;
  v13->m_fPosAbs[3] = (float)v11->m_nMapMaxSize[2] - pRT[2];
  v13->m_fPosAbs[4] = (float)(-0.0 - (float)v11->m_nMapMinSize[0]) + *pLB;
  v13->m_fPosAbs[5] = (float)v11->m_nMapMaxSize[2] - pLB[2];
  v13->m_fPosAbs[6] = (float)(-0.0 - (float)v11->m_nMapMinSize[0]) + *v15;
  v13->m_fPosAbs[7] = (float)v11->m_nMapMaxSize[2] - v15[2];
  for ( j = 0; j < 4; ++j )
  {
    v13->m_fScrNor[2 * j] = (float)(v13->m_fPosAbs[2 * j] * (float)prcWnd->right) / (float)v11->m_nMapSize[0];
    v13->m_fScrNor[2 * j + 1] = (float)(v13->m_fPosAbs[2 * j + 1] * (float)prcWnd->bottom) / (float)v11->m_nMapSize[2];
  }
}
