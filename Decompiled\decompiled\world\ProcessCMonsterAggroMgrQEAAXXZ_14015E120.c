/*
 * Function: ?Process@CMonsterAggroMgr@@QEAAXXZ
 * Address: 0x14015E120
 */

void __fastcall CMonsterAggroMgr::Process(CMonsterAggroMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int v4; // [sp+20h] [bp-18h]@4
  CMonsterAggroMgr *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = GetLoopTime();
  if ( v5->m_dwAggroCount )
  {
    if ( v4 - v5->m_dwAllResetLastTime >= v5->m_dwAllResetTimer )
    {
      CMonsterAggroMgr::Init(v5);
      v5->m_dwAllResetLastTime = v4;
    }
    if ( (signed int)(v4 - v5->m_dwShortRankLastTime) >= (signed int)v5->m_dwShortRankTimer )
    {
      CMonsterAggroMgr::_ShortRank(v5);
      CMonsterAggroMgr::SendChangeAggroData(v5);
      v5->m_dwShortRankLastTime = v4;
    }
  }
  else
  {
    v5->m_dwShortRankLastTime = v4;
    v5->m_dwAllResetLastTime = v4;
  }
}
