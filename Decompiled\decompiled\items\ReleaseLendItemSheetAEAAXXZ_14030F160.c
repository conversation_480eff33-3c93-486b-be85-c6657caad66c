/*
 * Function: ?Release@LendItemSheet@@AEAAXXZ
 * Address: 0x14030F160
 */

void __fastcall LendItemSheet::Release(LendItemSheet *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  LendItemSheet *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ListHeap<LendItemSheet::Cell>::Release(&v4->_heapFixRow);
}
