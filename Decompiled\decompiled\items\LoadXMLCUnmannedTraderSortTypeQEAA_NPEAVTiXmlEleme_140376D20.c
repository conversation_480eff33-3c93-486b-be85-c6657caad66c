/*
 * Function: ?LoadXML@CUnmannedTraderSortType@@QEAA_NPEAVTiXmlElement@@AEAVCLogFile@@K@Z
 * Address: 0x140376D20
 */

char __fastcall CUnmannedTraderSortType::LoadXML(CUnmannedTraderSortType *this, TiXmlElement *pkElemSortType, CLogFile *kLogger, unsigned int dwDivisionID)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  char *Source; // [sp+20h] [bp-18h]@4
  char *v9; // [sp+28h] [bp-10h]@6
  CUnmannedTraderSortType *v10; // [sp+40h] [bp+8h]@1
  TiXmlElement *v11; // [sp+48h] [bp+10h]@1
  CLogFile *v12; // [sp+50h] [bp+18h]@1
  unsigned int v13; // [sp+58h] [bp+20h]@1

  v13 = dwDivisionID;
  v12 = kLogger;
  v11 = pkElemSortType;
  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  Source = (char *)TiXmlElement::Attribute(pkElemSortType, "type");
  if ( Source )
  {
    v9 = (char *)TiXmlElement::Attribute(v11, "query");
    if ( v9 )
    {
      strcpy_0(v10->m_szTypeName, Source);
      strcpy_0(v10->m_szQuery, v9);
      result = 1;
    }
    else
    {
      CLogFile::Write(
        v12,
        "CUnmannedTraderSortType::LoadXML( TiXmlElement * pkElement, CLogFile & pkLogger )\r\n"
        "\t\tDivisionID(%u), ClassID(%u) pkElemSortType->Attribute( query ) NULL!\r\n",
        v13,
        v10->m_dwID);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      v12,
      "CUnmannedTraderSortType::LoadXML( TiXmlElement * pkElement, CLogFile & pkLogger )\r\n"
      "\t\tDivisionID(%u), ClassID(%u) pkElemSortType->Attribute( type ) NULL!\r\n",
      v13,
      v10->m_dwID);
    result = 0;
  }
  return result;
}
