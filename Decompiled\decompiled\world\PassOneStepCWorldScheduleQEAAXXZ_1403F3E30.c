/*
 * Function: ?PassOneStep@CWorldSchedule@@QEAAXXZ
 * Address: 0x1403F3E30
 */

void __fastcall CWorldSchedule::PassOneStep(CWorldSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  _base_fld *v5; // [sp+28h] [bp-10h]@6
  CWorldSchedule *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  n = v6->m_nSchCursor + 1;
  if ( n >= v6->m_nMaxSchNum )
    n = 0;
  v5 = CRecordData::GetRecord(&v6->m_tblSch, n);
  v6->m_nCurHour = v5[1].m_dwIndex;
  v6->m_nCurMin = *(_DWORD *)&v5[1].m_strCode[0];
  v6->m_nCurMilSec = 0;
}
