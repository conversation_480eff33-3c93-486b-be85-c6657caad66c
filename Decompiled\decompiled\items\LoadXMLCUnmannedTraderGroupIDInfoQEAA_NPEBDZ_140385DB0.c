/*
 * Function: ?LoadXML@CUnmannedTraderGroupIDInfo@@QEAA_NPEBD@Z
 * Address: 0x140385DB0
 */

char __fastcall CUnmannedTraderGroupIDInfo::LoadXML(CUnmannedTraderGroupIDInfo *this, const char *szFileName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CUnmannedTraderDivisionInfo *v5; // rax@21
  __int64 v6; // [sp+0h] [bp-148h]@1
  unsigned int v7; // [sp+20h] [bp-128h]@17
  TiXmlDocument v8; // [sp+40h] [bp-108h]@6
  TiXmlNode *v9; // [sp+B8h] [bp-90h]@8
  TiXmlElement *pkElement; // [sp+C0h] [bp-88h]@10
  unsigned int dwID; // [sp+D4h] [bp-74h]@12
  char *szName; // [sp+E8h] [bp-60h]@12
  CUnmannedTraderDivisionInfo *_Val; // [sp+F8h] [bp-50h]@12
  unsigned int v14; // [sp+104h] [bp-44h]@12
  char v15; // [sp+108h] [bp-40h]@7
  char v16; // [sp+109h] [bp-3Fh]@9
  char v17; // [sp+10Ah] [bp-3Eh]@11
  char v18; // [sp+10Bh] [bp-3Dh]@15
  char v19; // [sp+10Ch] [bp-3Ch]@17
  char v20; // [sp+10Dh] [bp-3Bh]@19
  CUnmannedTraderDivisionInfo *v21; // [sp+110h] [bp-38h]@23
  CUnmannedTraderDivisionInfo *v22; // [sp+118h] [bp-30h]@20
  char v23; // [sp+120h] [bp-28h]@24
  char v24; // [sp+121h] [bp-27h]@26
  char v25; // [sp+122h] [bp-26h]@28
  __int64 v26; // [sp+128h] [bp-20h]@4
  CUnmannedTraderDivisionInfo *v27; // [sp+130h] [bp-18h]@21
  CUnmannedTraderGroupIDInfo *v28; // [sp+150h] [bp+8h]@1
  const char *v29; // [sp+158h] [bp+10h]@1

  v29 = szFileName;
  v28 = this;
  v2 = &v6;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v26 = -2i64;
  if ( szFileName )
  {
    TiXmlDocument::TiXmlDocument(&v8);
    if ( TiXmlDocument::LoadFile(&v8, 0) )
    {
      v9 = TiXmlNode::FirstChild((TiXmlNode *)&v8.vfptr, "UnmannedTrader");
      if ( v9 )
      {
        pkElement = TiXmlNode::FirstChildElement(v9, "division");
        if ( pkElement )
        {
          dwID = -1;
          szName = 0i64;
          _Val = 0i64;
          v14 = 0;
          while ( pkElement )
          {
            dwID = -1;
            if ( !TiXmlElement::Attribute(pkElement, "id", (int *)&dwID) )
            {
              CUnmannedTraderGroupIDInfo::Log(
                v28,
                "CUnmannedTraderGroupItemInfoTable::LoadXML( szFileName(%s) )\r\n"
                "\t\t0 == pkElement->Attribute( id, &iID ) Fail!\r\n",
                v29);
              v18 = 0;
              TiXmlDocument::~TiXmlDocument(&v8);
              return v18;
            }
            if ( CUnmannedTraderGroupIDInfo::IsExistID(v28, dwID) )
            {
              v7 = dwID;
              CUnmannedTraderGroupIDInfo::Log(
                v28,
                "CUnmannedTraderGroupItemInfoTable::LoadXML( szFileName(%s) )\r\n"
                "\t\t%dth Division IsExistID( iID )( iID(%u) ) Exist!\r\n",
                v29,
                v14);
              v19 = 0;
              TiXmlDocument::~TiXmlDocument(&v8);
              return v19;
            }
            szName = (char *)TiXmlElement::Attribute(pkElement, "name");
            if ( !szName )
            {
              CUnmannedTraderGroupIDInfo::Log(
                v28,
                "CUnmannedTraderGroupItemInfoTable::LoadXML( szFileName(%s) )\r\n"
                "\t\t0 == pkElement->Attribute( name ) Fail!\r\n",
                v29);
              v20 = 0;
              TiXmlDocument::~TiXmlDocument(&v8);
              return v20;
            }
            v22 = (CUnmannedTraderDivisionInfo *)operator new(0xD8ui64);
            if ( v22 )
            {
              CUnmannedTraderDivisionInfo::CUnmannedTraderDivisionInfo(v22, dwID, szName);
              v27 = v5;
            }
            else
            {
              v27 = 0i64;
            }
            v21 = v27;
            _Val = v27;
            if ( !v27 )
            {
              v7 = dwID;
              CUnmannedTraderGroupIDInfo::Log(
                v28,
                "CUnmannedTraderGroupItemInfoTable::LoadXML( szFileName(%s) )\r\n"
                "\t\t%dth Division new CUnmannedTraderDivisionInfo(iID(%d) NULL!\r\n",
                v29,
                v14);
              v23 = 0;
              TiXmlDocument::~TiXmlDocument(&v8);
              return v23;
            }
            if ( !CUnmannedTraderDivisionInfo::LoadXML(_Val, pkElement, v28->m_pkLogger) )
            {
              v24 = 0;
              TiXmlDocument::~TiXmlDocument(&v8);
              return v24;
            }
            std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::push_back(
              &v28->m_vecDivisionInfo,
              &_Val);
            pkElement = TiXmlNode::NextSiblingElement((TiXmlNode *)&pkElement->vfptr, "division");
            ++v14;
          }
          v25 = 1;
          TiXmlDocument::~TiXmlDocument(&v8);
          result = v25;
        }
        else
        {
          CUnmannedTraderGroupIDInfo::Log(
            v28,
            "CUnmannedTraderGroupItemInfoTable::LoadXML( szFileName(%s) )\r\n"
            "\t\tnode->FirstChildElement( division ) Fail!\r\n",
            v29);
          v17 = 0;
          TiXmlDocument::~TiXmlDocument(&v8);
          result = v17;
        }
      }
      else
      {
        CUnmannedTraderGroupIDInfo::Log(
          v28,
          "CUnmannedTraderGroupItemInfoTable::LoadXML( szFileName(%s) )\r\n"
          "\t\tTiXmlDocument::FirstChild(UnmannedTrader) NULL!\r\n",
          v29);
        v16 = 0;
        TiXmlDocument::~TiXmlDocument(&v8);
        result = v16;
      }
    }
    else
    {
      CUnmannedTraderGroupIDInfo::Log(
        v28,
        "CUnmannedTraderGroupItemInfoTable::LoadXML( szFileName(%s) )\r\n\t\tTiXmlDocument::LoadFile() Fail!\r\n",
        v29);
      v15 = 0;
      TiXmlDocument::~TiXmlDocument(&v8);
      result = v15;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
