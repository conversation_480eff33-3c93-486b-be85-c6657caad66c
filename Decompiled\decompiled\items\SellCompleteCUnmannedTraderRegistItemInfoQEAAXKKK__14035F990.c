/*
 * Function: ?SellComplete@CUnmannedTraderRegistItemInfo@@QEAAXKKK_JQEBD1@Z
 * Address: 0x14035F990
 */

void __fastcall CUnmannedTraderRegistItemInfo::SellComplete(CUnmannedTraderRegistItemInfo *this, unsigned int dwPrice, unsigned int dwBuyerSerial, unsigned int dwTax, __int64 tResultTime, const char *const wszBuyerName, const char *const szBuyerAccount)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  CUnmannedTraderRegistItemInfo *v10; // [sp+30h] [bp+8h]@1

  v10 = this;
  v7 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v7 = -*********;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v10->m_dwPrice = dwPrice;
  v10->m_dwBuyerSerial = dwBuyerSerial;
  v10->m_dwTax = dwTax;
  v10->m_tResultTime = tResultTime;
  strcpy_s(v10->m_wszBuyerName, 0x11ui64, wszBuyerName);
  strcpy_s(v10->m_szBuyerAccount, 0xDui64, szBuyerAccount);
  CUnmannedTraderItemState::Set(&v10->m_kState, 3);
}
