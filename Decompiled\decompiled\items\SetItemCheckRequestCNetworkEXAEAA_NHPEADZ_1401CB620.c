/*
 * Function: ?SetItemCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB620
 */

char __fastcall CNetworkEX::SetItemCheckRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  bool bSet; // [sp+20h] [bp-28h]@6
  char *v8; // [sp+30h] [bp-18h]@4
  CPlayer *v9; // [sp+38h] [bp-10h]@4

  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( v9->m_bOper )
  {
    bSet = *v8;
    CPlayer::pc_SetItemCheckRequest(v9, *(_DWORD *)(v8 + 1), v8[5], v8[6], bSet);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
