/*
 * Function: ?RadarProc@CRadarItemMgr@@QEAA_NPEAU_RadarItem_fld@@@Z
 * Address: 0x1402E4E30
 */

char __fastcall CRadarItemMgr::RadarProc(CRadarItemMgr *this, _RadarItem_fld *pRadarFld)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  CPvpUserAndGuildRankingSystem *v5; // rax@36
  unsigned int v6; // eax@36
  CPvpUserAndGuildRankingSystem *v7; // rax@47
  unsigned int v8; // eax@47
  __int64 v9; // [sp+0h] [bp-78h]@1
  CPartyPlayer *v10; // [sp+20h] [bp-58h]@12
  char v11; // [sp+28h] [bp-50h]@12
  int j; // [sp+2Ch] [bp-4Ch]@23
  int k; // [sp+30h] [bp-48h]@33
  int v14; // [sp+34h] [bp-44h]@22
  char v15; // [sp+38h] [bp-40h]@22
  CMapData *v16; // [sp+40h] [bp-38h]@22
  char v17; // [sp+48h] [bp-30h]@23
  CPlayer *pkObj; // [sp+50h] [bp-28h]@29
  unsigned int v19; // [sp+58h] [bp-20h]@29
  unsigned __int8 v20; // [sp+5Ch] [bp-1Ch]@29
  char *v21; // [sp+60h] [bp-18h]@64
  CRadarItemMgr *v22; // [sp+80h] [bp+8h]@1
  _RadarItem_fld *v23; // [sp+88h] [bp+10h]@1

  v23 = pRadarFld;
  v22 = this;
  v2 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CRadarItemMgr::ResetFlags(v22);
  _detected_char_list::init(&v22->m_RadarResult);
  if ( v22->m_bUse && v22->m_pMaster && v22->m_pDestMap && v23 )
  {
    if ( v22->m_bPlayerEnd && v22->m_bMonEnd )
    {
      result = 0;
    }
    else
    {
      v10 = v22->m_pMaster->m_pPartyMgr;
      v11 = CPlayerDB::GetRaceCode(&v22->m_pMaster->m_Param);
      if ( v23->m_strEffSort[0] == 49 )
        v22->m_bSameRace = 1;
      if ( v23->m_strEffSort[1] == 49 )
        v22->m_bNorDiffRace = 1;
      if ( v23->m_strEffSort[2] == 49 )
        v22->m_bChiefDiffRace = 1;
      if ( v23->m_strEffSort[3] == 49 )
        v22->m_bEliteMonster = 1;
      if ( !v22->m_bEliteMonster )
        v22->m_bMonEnd = 1;
      v14 = 0;
      v15 = 0;
      v16 = 0i64;
      if ( !v22->m_bPlayerEnd )
      {
        v17 = 0;
        for ( j = v22->m_nPlayerNum; j < 2532; ++j )
        {
          if ( v14 >= 50 || v14 < 0 )
          {
            v22->m_nPlayerNum = j;
            v15 = 1;
            break;
          }
          pkObj = &g_Player + j;
          v19 = CPlayerDB::GetCharSerial(&pkObj->m_Param);
          v20 = CPlayerDB::GetRaceCode(&pkObj->m_Param);
          v16 = pkObj->m_pCurMap;
          if ( pkObj->m_bLive && v22->m_pDestMap == v16 )
          {
            if ( !v22->m_bChiefDiffRace || (unsigned __int8)v11 == v20 )
              goto LABEL_74;
            for ( k = 0; k < 3; ++k )
            {
              if ( (unsigned __int8)v11 != k )
              {
                v5 = CPvpUserAndGuildRankingSystem::Instance();
                v6 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, k, 0);
                if ( v19 == v6 )
                {
                  v14 = _detected_char_list::AddCharInfo(&v22->m_RadarResult, 3, pkObj->m_fCurPos);
                  v17 = 1;
                  break;
                }
              }
            }
            if ( v17 )
            {
              v17 = 0;
            }
            else
            {
LABEL_74:
              if ( v22->m_bNorDiffRace && (unsigned __int8)v11 != v20 )
              {
                v14 = _detected_char_list::AddCharInfo(&v22->m_RadarResult, 2, pkObj->m_fCurPos);
              }
              else if ( v22->m_bSameRace && (unsigned __int8)v11 == v20 && v22->m_pMaster != pkObj )
              {
                v7 = CPvpUserAndGuildRankingSystem::Instance();
                v8 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v7, v11, 0);
                if ( v19 == v8 )
                {
                  v14 = _detected_char_list::AddCharInfo(&v22->m_RadarResult, 1, pkObj->m_fCurPos);
                }
                else if ( CPartyPlayer::IsPartyMode(v10) )
                {
                  if ( !CPartyPlayer::IsPartyMember(v10, pkObj) )
                    v14 = _detected_char_list::AddCharInfo(&v22->m_RadarResult, 0, pkObj->m_fCurPos);
                }
                else
                {
                  v14 = _detected_char_list::AddCharInfo(&v22->m_RadarResult, 0, pkObj->m_fCurPos);
                }
              }
            }
          }
        }
        if ( !v15 )
        {
          v22->m_nPlayerNum = j;
          v22->m_bPlayerEnd = 1;
        }
      }
      if ( !v22->m_bMonEnd )
      {
        v15 = 0;
        for ( j = v22->m_nMonNum; j < 30000; ++j )
        {
          if ( v14 >= 50 || v14 < 0 )
          {
            v22->m_nMonNum = j;
            v15 = 1;
            break;
          }
          v21 = (char *)g_Monster + 6424 * j;
          v16 = (CMapData *)*((_QWORD *)v21 + 11);
          if ( v21[24] && v22->m_pDestMap == v16 && *(_DWORD *)(*((_QWORD *)v21 + 241) + 196i64) == 2 )
            v14 = _detected_char_list::AddCharInfo(&v22->m_RadarResult, 4, (float *)v21 + 10);
        }
        if ( !v15 )
        {
          v22->m_nMonNum = j;
          v22->m_bMonEnd = 1;
        }
      }
      result = 1;
    }
  }
  else
  {
    v22->m_bPlayerEnd = 1;
    v22->m_bMonEnd = 1;
    result = 0;
  }
  return result;
}
