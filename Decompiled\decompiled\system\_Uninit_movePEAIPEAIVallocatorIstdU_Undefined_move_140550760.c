/*
 * Function: ??$_Uninit_move@PEAIPEAIV?$allocator@I@std@@U_Undefined_move_tag@2@@std@@YAPEAIPEAI00AEAV?$allocator@I@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140550760
 */

unsigned int *__fastcall std::_Uninit_move<unsigned int *,unsigned int *,std::allocator<unsigned int>,std::_Undefined_move_tag>(unsigned int *a1, unsigned int *a2, unsigned int *a3, std::allocator<unsigned int> *a4)
{
  return stdext::unchecked_uninitialized_copy<unsigned int *,unsigned int *,std::allocator<unsigned int>>(
           a1,
           a2,
           a3,
           a4);
}
