/*
 * Function: ??$push@PEAVCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEAVCMonster@@@Z
 * Address: 0x14040A3F0
 */

void __fastcall lua_tinker::push<CMonster *>(struct lua_State *L, CMonster *ret)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  struct lua_State *La; // [sp+30h] [bp+8h]@1

  La = L;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  lua_tinker::type2lua<CMonster *>(La, ret);
}
