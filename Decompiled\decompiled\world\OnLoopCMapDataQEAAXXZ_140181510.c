/*
 * Function: ?OnLoop@CMapData@@QEAAXXZ
 * Address: 0x140181510
 */

void __fastcall CMapData::OnLoop(CMapData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  CMapData *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v5->m_tmrMineGradeReSet) )
  {
    for ( j = 0; j < v5->m_nResDumNum; ++j )
      _res_dummy::SetRangeGrade(&v5->m_pResDummy[j]);
  }
}
