/*
 * RF Online Game Structures
 * Compatible with Visual Studio 2022
 * Extracted from decompiled server code
 */

#pragma once

#include <cstdint>
#include <string>

namespace RFOnline {
namespace GameStructures {

// ============================================================================
// PLAYER SYSTEM STRUCTURES
// ============================================================================

// Player Database Structure
struct CPlayerDB {
    struct CharacterData {
        double m_dExp;              // Experience points (double precision)
        uint32_t m_dwLevel;         // Character level
        uint32_t m_dwHP;            // Health points
        uint32_t m_dwFP;            // Force points (mana)
        uint32_t m_dwSP;            // Stamina points
        // Additional character stats
    } m_dbChar;
    
    double GetExp() const { return m_dbChar.m_dExp; }
};

// Player Class
class CPlayer {
public:
    // Experience and leveling
    void CalcExp(void* pDst, int nDam, void* kPartyExpNotify);
    void AlterExp(double dExp, int nType, int nParam1, int nParam2);
    bool SetMaxLevelLimit(uint32_t dwLevel);
    
    // Combat system
    int GetAttackDamPoint(int nAttPnt, int nAttPart, int nTolType, void* pDst, bool bBackAttack);
    bool SF_AllContDamageRemove_Once(void* pTargetChar);
    bool SF_AllContDamageForceRemove_Once();
    
    // Quest system
    void SendMsg_SelectQuestReward(int nQuestSlot);
    void Emb_CompleteQuest(int nQuestSlot, int nParam1, int nParam2);
    
    // Inventory and items
    template<typename T>
    class TInventory {
    public:
        bool Push(const T& item);
        bool Pop(T& item);
        bool IsFull() const;
        size_t GetCount() const;
    };
    
private:
    void* m_pCurMap;
    float m_fCurPos[3];     // X, Y, Z coordinates
    uint32_t m_dwSerial;    // Player serial number
};

// Character Base Class
class CCharacter {
public:
    struct VTable {
        int (*GetHP)(CCharacter* pThis);
        int (*SF_DamageAndStun)(CCharacter* pThis);
        int (*SF_AllContDamageForceRemove_Once)(CCharacter* pThis);
        int (*SF_ContDamageTimeInc_Once)(CCharacter* pThis);
    } *vfptr;
    
    void* m_pCurMap;
    float m_fCurPos[3];
    
    // Virtual functions
    virtual int GetHP() = 0;
    virtual int SF_DamageAndStun() = 0;
    virtual int SF_AllContDamageForceRemove_Once() = 0;
    virtual int SF_ContDamageTimeInc_Once() = 0;
};

// ============================================================================
// MONSTER SYSTEM STRUCTURES
// ============================================================================

// Monster AI System
class CMonsterAI {
public:
    enum AIState {
        STATE_PASSIVE = 0,
        STATE_ATTACK = 1,
        STATE_BATTLE = 2,
        STATE_RETURN = 3
    };
    
    AIState m_eCurrentState;
    uint32_t m_dwStateTime;
    void* m_pTarget;
    
    bool CheckSPF_MON_MOTIVE_DF(void* pSkill, int nMotiveValue, void* pMon, void** ppTar);
    void* GetWisdomTarget(void* pAI, void* pMon);
};

// Monster Class
class CMonster : public CCharacter {
public:
    struct MonsterLootIndex {
        int nStartRecIndex;
        int nEndRecIndex;
    };
    
    static MonsterLootIndex s_idxMonsterLoot[10000];  // Monster loot table indices
    
    void* m_pRecordSet;     // Monster data record
    CMonsterAI* m_pAI;      // AI controller
    uint32_t m_dwIndex;     // Monster type index
};

// Monster Set Info (for drop rates)
struct MonsterSetInfoData {
    uint32_t m_iMonsterLootRateSame;           // Drop rate for same level
    uint32_t m_iMonsterLootingRateUp[11];      // Drop rate for higher level monsters
    uint32_t m_iMonsterLootingRateDown[11];    // Drop rate for lower level monsters
    
    uint32_t GetMonsterDropRate(int iDiffLevel);
};

// ============================================================================
// COMBAT SYSTEM STRUCTURES
// ============================================================================

// Attack Parameters
struct _attack_param {
    void* pDst;             // Target character
    int nPart;              // Attack part (head, body, etc.)
    int nTol;               // Tolerance type
    bool bBackAttack;       // Is back attack
    bool bMatchless;        // Instant kill flag
};

// Damage List Entry
struct _be_damaged_char {
    CCharacter* m_pChar;    // Damaged character
    int m_nDamage;          // Damage amount
};

// Attack Class
class CAttack {
public:
    CCharacter* m_pAttChar;         // Attacking character
    _attack_param* m_pp;            // Attack parameters
    _be_damaged_char* m_DamList;    // Damage list
    int m_nDamagedObjNum;           // Number of damaged objects
    
    void CalcAvgDamage();
    void AreaDamageProc(int nLimitRadius, int nAttPower, float* pTar, int nEffAttPower, bool bUseEffBullet);
};

// ============================================================================
// QUEST SYSTEM STRUCTURES
// ============================================================================

// Quest Database Entry
struct QuestSlotData {
    uint8_t byQuestType;    // Quest type (255 = empty slot)
    uint32_t dwQuestID;     // Quest identifier
    uint32_t dwStartTime;   // Quest start time
    uint32_t dwEndTime;     // Quest end time
    uint8_t byProgress;     // Quest progress
    // Additional quest data
};

// Quest Database (30 slots per player)
struct QuestDatabase {
    QuestSlotData m_List[30];
    
    bool IsSlotEmpty(int nSlot) const {
        return m_List[nSlot].byQuestType == 255;
    }
};

// ============================================================================
// GUILD SYSTEM STRUCTURES
// ============================================================================

// Guild Battle Controller
class CGuildBattleController {
public:
    static CGuildBattleController* Instance();
    
    bool CheatGetStone(void* pPlayer);
    void ProcessGuildBattle();
    
private:
    struct GuildBattleData {
        uint32_t dwGuildSerial;
        char szGuildName[25];
        uint32_t dwWinCount;
        uint32_t dwLossCount;
        uint8_t byTaxRate;
    };
    
    std::vector<GuildBattleData> m_vecGuildData;
};

// ============================================================================
// ITEM SYSTEM STRUCTURES
// ============================================================================

// Storage List (Inventory Item)
struct _STORAGE_LIST {
    struct _db_con {
        uint8_t m_byTableCode;      // Item table code
        uint16_t m_wItemIndex;      // Item index
        uint32_t m_dwDur;           // Durability
        uint32_t m_dwLv;            // Item level/upgrade
        uint64_t m_lnUID;           // Unique item ID
        
        _db_con() : m_byTableCode(0), m_wItemIndex(0), m_dwDur(0), m_dwLv(0), m_lnUID(0) {}
    };
};

// Item Drop Manager
class CItemDropMgr {
public:
    struct DropItem {
        uint8_t byTableCode;
        void* pFld;
        uint32_t dwDur;
        float fDropPos[3];      // X, Y, Z position
    };
    
    struct DropNode {
        DropItem m_DropItem;
        uint32_t m_dwDropCount;
    };
    
    bool Drop(int nCnt);
    bool FrontDrop();
    DropNode* GetFrontPtr();
    void PopFront();
    
private:
    std::vector<DropNode> m_vecDropList;
};

// Item Functions
bool IsOverLapItem(uint8_t byTableCode);
uint32_t GetItemDurPoint(uint8_t byTableCode, uint32_t dwIndex);
uint8_t GetDefItemUpgSocketNum(uint8_t byTableCode, uint32_t dwIndex);
uint32_t GetBitAfterSetLimSocket(int nSocketNum);

// ============================================================================
// MAP SYSTEM STRUCTURES
// ============================================================================

// Map Data
class CMapData {
public:
    int GetSectorIndex(float* pPos);
    
private:
    struct SectorInfo {
        float fMinX, fMinY, fMaxX, fMaxY;
        uint32_t dwSectorID;
    };
    
    std::vector<SectorInfo> m_vecSectors;
};

// Security Info
struct _sec_info {
    uint32_t dwSecurityLevel;
    bool bPvPEnabled;
    bool bGuildBattleZone;
};

} // namespace GameStructures
} // namespace RFOnline
