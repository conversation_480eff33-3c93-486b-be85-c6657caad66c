/*
 * Function: j_?_<PERSON><PERSON>@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@KAXXZ
 * Address: 0x140010C67
 */

void __noreturn std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_<PERSON>len()
{
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_<PERSON><PERSON>();
}
