/*
 * Function: ??$SearchSlotIndex@UORDER_INC@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@QEAAKAEBU_State@CLuaLooting_Novus_Item@@@Z
 * Address: 0x140405F80
 */

unsigned int __fastcall US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::SearchSlotIndex<US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::ORDER_INC>(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *this, CLuaLooting_Novus_Item::_State *state)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *p; // [sp+30h] [bp+8h]@1

  p = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::ORDER_INC::SearchSlotIndex(p, state);
}
