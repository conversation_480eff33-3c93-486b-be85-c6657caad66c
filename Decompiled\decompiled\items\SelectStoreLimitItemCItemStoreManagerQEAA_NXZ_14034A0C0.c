/*
 * Function: ?SelectStoreLimitItem@CItemStoreManager@@QEAA_NXZ
 * Address: 0x14034A0C0
 */

char __fastcall CItemStoreManager::SelectStoreLimitItem(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CItemStoreManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRFWorldDatabase::Select_StoreLimitItem(pkDB, &v5->m_Sheet) == 1 )
  {
    CItemStoreManager::Log(
      v5,
      "CItemStoreManager::SelectStoreLimitItem\r\n\t\tg_Main.m_pWorldDB->Select_StoreLimitItem() Fail!\r\n");
    result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
