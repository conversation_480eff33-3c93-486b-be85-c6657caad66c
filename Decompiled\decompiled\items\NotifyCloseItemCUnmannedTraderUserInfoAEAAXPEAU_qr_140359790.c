/*
 * Function: ?NotifyCloseItem@CUnmannedTraderUserInfo@@AEAAXPEAU_qry_case_unmandtrader_log_in_proc_update_complete@@PEAVCLogFile@@@Z
 * Address: 0x140359790
 */

void __fastcall CUnmannedTraderUserInfo::NotifyCloseItem(CUnmannedTraderUserInfo *this, _qry_case_unmandtrader_log_in_proc_update_complete *pkResult, CLogFile *pkLogger)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v5; // rax@13
  CUnmannedTraderRegistItemInfo *v6; // rax@14
  unsigned __int16 v7; // ax@14
  CUnmannedTraderRegistItemInfo *v8; // rax@15
  CUnmannedTraderRegistItemInfo *v9; // rax@15
  CUnmannedTraderRegistItemInfo *v10; // rax@15
  CUnmannedTraderRegistItemInfo *v11; // rax@15
  CUnmannedTraderRegistItemInfo *v12; // rax@16
  CUnmannedTraderRegistItemInfo *v13; // rax@16
  unsigned int v14; // eax@16
  CUnmannedTraderRegistItemInfo *v15; // rax@16
  CUnmannedTraderRegistItemInfo *v16; // rax@16
  CUnmannedTraderRegistItemInfo *v17; // rax@16
  CUnmannedTraderRegistItemInfo *v18; // rax@16
  CUnmannedTraderRegistItemInfo *v19; // rax@16
  unsigned __int16 v20; // ax@18
  __int64 v21; // [sp+0h] [bp-118h]@1
  CPlayer *v22; // [sp+30h] [bp-E8h]@4
  _STORAGE_LIST::_db_con *pRegItem; // [sp+38h] [bp-E0h]@10
  char Dst; // [sp+50h] [bp-C8h]@10
  unsigned __int8 v25; // [sp+51h] [bp-C7h]@16
  __int16 v26; // [sp+52h] [bp-C6h]@16
  int v27; // [sp+54h] [bp-C4h]@16
  int v28; // [sp+58h] [bp-C0h]@16
  int j; // [sp+C4h] [bp-54h]@10
  char pbyType; // [sp+D4h] [bp-44h]@17
  char v31; // [sp+D5h] [bp-43h]@17
  unsigned int v32; // [sp+E4h] [bp-34h]@7
  char *v33; // [sp+E8h] [bp-30h]@16
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v34; // [sp+F0h] [bp-28h]@16
  __int64 v35; // [sp+F8h] [bp-20h]@16
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v36; // [sp+100h] [bp-18h]@16
  CUnmannedTraderUserInfo *v37; // [sp+120h] [bp+8h]@1
  _qry_case_unmandtrader_log_in_proc_update_complete *v38; // [sp+128h] [bp+10h]@1
  CLogFile *v39; // [sp+130h] [bp+18h]@1

  v39 = pkLogger;
  v38 = pkResult;
  v37 = this;
  v3 = &v21;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v22 = CUnmannedTraderUserInfo::FindOwner(v37);
  if ( v22 && v22->m_bOper )
  {
    pRegItem = 0i64;
    memset_0(&Dst, 0, 0x66ui64);
    Dst = 1;
    for ( j = 0; j < 20; ++j )
    {
      v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v37->m_vecLoadItemInfo,
             j);
      if ( CUnmannedTraderRegistItemInfo::IsWaitNoitfyClose(v5) )
      {
        v6 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
               &v37->m_vecLoadItemInfo,
               j);
        v7 = CUnmannedTraderRegistItemInfo::GetItemSerial(v6);
        pRegItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v22->m_Param.m_dbInven.m_nListNum, v7);
        if ( pRegItem )
        {
          v33 = v22->m_szItemHistoryFileName;
          v34 = &v37->m_vecLoadItemInfo;
          v12 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          v35 = CUnmannedTraderRegistItemInfo::GetResultTime(v12);
          v36 = &v37->m_vecLoadItemInfo;
          v13 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          v14 = CUnmannedTraderRegistItemInfo::GetRegistSerial(v13);
          CMgrAvatorItemHistory::login_cancel_auto_trade(
            &CPlayer::s_MgrItemHistory,
            v22->m_ObjID.m_wIndex,
            v14,
            pRegItem,
            v35,
            v33);
          v15 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          *(int *)((char *)&v28 + 10 * v25) = CUnmannedTraderRegistItemInfo::GetPrice(v15);
          v16 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          *(&v26 + 5 * v25) = CUnmannedTraderRegistItemInfo::GetItemSerial(v16);
          v17 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          *(int *)((char *)&v27 + 10 * v25++) = CUnmannedTraderRegistItemInfo::GetRegistSerial(v17);
          v18 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::Clear(v18);
          v19 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::SetState(v19, 11);
        }
        else
        {
          v38->List[v38->wNum].byProcUpdate = 82;
          v8 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                 &v37->m_vecLoadItemInfo,
                 j);
          v38->List[v38->wNum].dwBuyer = CUnmannedTraderRegistItemInfo::GetBuyerSerial(v8);
          v9 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                 &v37->m_vecLoadItemInfo,
                 j);
          v38->List[v38->wNum].dwRegistSerial = CUnmannedTraderRegistItemInfo::GetRegistSerial(v9);
          v38->List[v38->wNum++].byUpdateState = 7;
          v10 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::Clear(v10);
          v11 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v37->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::SetState(v11, 7);
        }
      }
    }
    pbyType = 30;
    v31 = 38;
    if ( (signed int)v25 > 0 )
    {
      v20 = _unmannedtrader_continue_item_inform_zocl::size((_unmannedtrader_continue_item_inform_zocl *)&Dst);
      CNetProcess::LoadSendMsg(unk_1414F2088, v37->m_wInx, &pbyType, &Dst, v20);
    }
  }
  else
  {
    if ( v22 )
      v32 = v22->m_bOper;
    else
      v32 = -1;
    CLogFile::Write(
      v39,
      "CUnmannedTraderUserInfo::NotifyCloseItem()\r\n\t\t( 0 == pkOwner || !pkOwner->m_bOper(%d) )\r\n",
      v32);
  }
}
