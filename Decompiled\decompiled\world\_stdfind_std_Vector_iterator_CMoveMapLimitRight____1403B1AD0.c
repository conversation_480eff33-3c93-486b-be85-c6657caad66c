/*
 * Function: _std::find_std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64__::_1_::dtor$2
 * Address: 0x1403B1AD0
 */

void __fastcall std::find_std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64__::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 56));
}
