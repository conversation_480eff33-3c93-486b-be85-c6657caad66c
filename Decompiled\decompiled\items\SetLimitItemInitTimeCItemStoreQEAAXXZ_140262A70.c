/*
 * Function: ?SetLimitItemInitTime@CItemStore@@QEAAXXZ
 * Address: 0x140262A70
 */

void __fastcall CItemStore::SetLimitItemInitTime(CItemStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  ATL::CTimeSpan *v3; // rax@6
  ATL::CTime *v4; // rax@6
  int v5; // eax@6
  ATL::CTime *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-C8h]@1
  __int64 _Time; // [sp+48h] [bp-80h]@4
  unsigned __int64 v9; // [sp+58h] [bp-70h]@4
  int lDays; // [sp+60h] [bp-68h]@4
  ATL::CTime v11; // [sp+78h] [bp-50h]@6
  unsigned __int64 v12; // [sp+88h] [bp-40h]@6
  ATL::CTime result; // [sp+90h] [bp-38h]@6
  ATL::CTimeSpan v14; // [sp+98h] [bp-30h]@6
  ATL::CTime v15; // [sp+A0h] [bp-28h]@6
  ATL::CTimeSpan *v16; // [sp+A8h] [bp-20h]@6
  int nDay; // [sp+B0h] [bp-18h]@6
  int nMonth; // [sp+B4h] [bp-14h]@6
  CItemStore *v19; // [sp+D0h] [bp+8h]@1

  v19 = this;
  v1 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = 60000i64 * v19->m_pRec->m_nLimitItem_InitTime;
  lDays = v9 / 0x5265C00;
  time_7(&_Time);
  if ( lDays > 0 )
  {
    ATL::CTimeSpan::CTimeSpan(&v14, lDays, 0, 0, 0);
    v16 = v3;
    v4 = ATL::CTime::GetTickCount(&result);
    ATL::CTime::operator+(v4, &v11, (ATL::CTimeSpan)v16->m_timeSpan);
    nDay = ATL::CTime::GetDay(&v11);
    nMonth = ATL::CTime::GetMonth(&v11);
    v5 = ATL::CTime::GetYear(&v11);
    ATL::CTime::CTime(&v15, v5, nMonth, nDay, 0, 0, 0, -1);
    v12 = ATL::CTime::GetTime(v6);
    v19->m_dwLimitInitTime = v12;
  }
  else
  {
    v19->m_dwLimitInitTime = v9 + _Time;
  }
}
