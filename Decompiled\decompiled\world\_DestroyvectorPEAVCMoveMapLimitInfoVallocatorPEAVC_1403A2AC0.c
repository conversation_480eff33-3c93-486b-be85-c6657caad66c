/*
 * Function: ?_Destroy@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAXPEAPEAVCMoveMapLimitInfo@@0@Z
 * Address: 0x1403A2AC0
 */

void __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Destroy(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Destroy_range<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(_First, _Last, &v6->_Alval);
}
