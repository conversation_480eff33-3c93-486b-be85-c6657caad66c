/*
 * Function: ?SearchPatrolMovePos@CMonsterHelper@@SAHPEAVCMonster@@AEAY02M@Z
 * Address: 0x140159F20
 */

signed __int64 __fastcall CMonsterHelper::SearchPatrolMovePos(CMonster *mon, float (*NewTar)[3])
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v5; // xmm0_4@19
  float v6; // xmm0_4@19
  float v7; // xmm0_4@19
  float v8; // xmm0_4@20
  float v9; // xmm0_4@20
  float v10; // xmm1_4@20
  __int64 v11; // [sp+0h] [bp-88h]@1
  char Src; // [sp+28h] [bp-60h]@24
  float v13; // [sp+2Ch] [bp-5Ch]@27
  CMonster *v14; // [sp+48h] [bp-40h]@7
  int j; // [sp+50h] [bp-38h]@7
  float v16; // [sp+54h] [bp-34h]@15
  int v17; // [sp+58h] [bp-30h]@15
  float v18; // [sp+5Ch] [bp-2Ch]@15
  double v19; // [sp+60h] [bp-28h]@19
  int v20; // [sp+68h] [bp-20h]@20
  int v21; // [sp+6Ch] [bp-1Ch]@21
  int v22; // [sp+70h] [bp-18h]@13
  float v23; // [sp+74h] [bp-14h]@20
  float v24; // [sp+78h] [bp-10h]@20
  CMonster *v25; // [sp+90h] [bp+8h]@1
  float *Pos; // [sp+98h] [bp+10h]@1

  Pos = (float *)NewTar;
  v25 = mon;
  v2 = &v11;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CMonster::IsRoateMonster(v25) )
    return 0i64;
  if ( !v25->m_bMove )
  {
    v14 = CMonsterHierarchy::GetParent(&v25->m_MonHierarcy);
    for ( j = 0; ; ++j )
    {
      if ( j >= 5 )
        return 0i64;
      if ( !v14 && v25->m_pDumPosition )
      {
        if ( !CMapData::GetRandPosInDummy(v25->m_pCurMap, v25->m_pDumPosition, Pos, 1) )
          continue;
      }
      else
      {
        if ( rand() % 2 )
          v22 = 0;
        else
          v22 = 0x7FFF;
        v16 = (float)((v22 + rand()) % 0xFFFF);
        v17 = v25->m_pMonRec->m_nMaxMoveDistance - v25->m_pMonRec->m_nMinMoveDistance;
        v18 = (float)v25->m_pMonRec->m_nMinMoveDistance;
        if ( v17 > 0 )
          v18 = v18 + (float)(rand() % v17);
        if ( v18 <= 2.0 )
          return 0i64;
        v19 = 6.283185307 * v16 / 65535.0;
        v5 = sin_0(v19);
        *Pos = v25->m_fCurPos[0] - (float)(v5 * v18);
        v6 = cos_0(v19);
        Pos[2] = v25->m_fCurPos[2] - (float)(v6 * v18);
        v7 = v25->m_fCurPos[1];
        Pos[1] = v7;
        if ( v14 )
        {
          Get3DSqrt(Pos, v14->m_fCurPos);
          v20 = (signed int)ffloor(v7);
          v8 = (float)v20;
          v23 = (float)v20;
          ((void (__fastcall *)(CMonster *))v25->vfptr->GetWidth)(v25);
          v9 = (float)(v8 / 2.0) + 100.0;
          v24 = v9;
          ((void (__fastcall *)(CMonster *))v14->vfptr->GetWidth)(v14);
          v10 = v24 + (float)(v9 / 2.0);
          if ( v23 > v10 )
          {
            Get3DSqrt(v25->m_fCurPos, v14->m_fCurPos);
            v21 = (signed int)ffloor(v10);
            if ( v20 > v21 )
              continue;
          }
        }
      }
      if ( (unsigned int)CBsp::CanYouGoThere(v25->m_pCurMap->m_Level.mBsp, v25->m_fCurPos, Pos, (float (*)[3])&Src) )
        return 1i64;
      if ( j >= 4 )
      {
        v13 = v25->m_fCurPos[1];
        memcpy_0(Pos, &Src, 0xCui64);
        return 1i64;
      }
    }
  }
  return 0i64;
}
