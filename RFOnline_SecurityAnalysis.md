# RF Online Server Security Analysis & Reverse Engineering Report

## Executive Summary

This document provides a comprehensive security analysis and reverse engineering report of the RF Online server based on decompiled C code. The analysis reveals critical security vulnerabilities, anti-cheat mechanisms, packet structures, and game balance formulas.

## 🔒 Anti-Cheat Mechanisms

### HackShield/nProtect GameGuard Integration

**System Architecture:**
- `CHackShieldExSystem` - Main anti-cheat controller
- `HACKSHEILD_PARAM_ANTICP` - Anti-CP (Copy Protection) module
- Session-based verification with checksum validation

**Key Components:**
```cpp
// Anti-cheat initialization
bool CHackShieldExSystem::Init() {
    return _AntiCpSvr_Initialize(".\\HackShield.crc");
}

// Session verification
bool OnCheckSession_FirstVerify(int nIndex);
bool OnRecvSession(int nIndex, char byProtocol, uint64_t tSize, char* pMsg);
```

**Protocols:**
- Protocol 1: Server checksum request
- Protocol 3: Client checksum response
- Continuous loop checking (every 50ms, checks 10 users per cycle)

### Speed Hack Detection

**Implementation:**
```cpp
struct SpeedHackDetection {
    uint32_t m_dwSpeedHackKey[4];      // 16-byte validation key
    uint32_t m_dwSendSpeedHackTime;    // Timestamp tracking
    uint32_t m_dwSpeedHackCount;       // Check counter
};

// Key generation (VULNERABLE - uses predictable rand())
for (int j = 0; j < 4; ++j) {
    int v9 = rand();
    int v13 = v9 << 16;
    pdwCode[j] = rand() + v13;
}
```

**Vulnerability:** Uses weak random number generation, making keys predictable.

### Mining Speed Controls

**Anti-cheat for resource gathering:**
- `MINE_SPEED_RATE` - Global mining speed multiplier
- `PCBANG_PRIMIUM_FAVOR_MINING_SPEED` - PC bang bonus rates
- Admin commands: `ct_minespeed`, `ct_pcminespeed`

## 📦 Packet Structures & Network Protocols

### Message Header Format

```cpp
struct _MSG_HEADER {
    uint16_t m_wSize;        // Total packet size
    uint8_t  m_byType1;      // Primary packet type  
    uint8_t  m_byType2;      // Secondary packet type
    uint32_t m_dwSerial;     // Packet serial number
};
```

### Encryption Systems

**1. FG (nProtect GameGuard) Encryption:**
```cpp
int _CcrFG_rs_EncryptPacket(void* context, uint8_t* data, int size);
int _CcrFG_rs_DecryptPacket(void* context, uint8_t* data, int size);
```

**2. Custom String Encryption:**
```cpp
void EnCryptString(char* pStr, int nSize, char byPlus, uint16_t wCryptKey) {
    for (int j = 0; j < nSize; ++j) {
        *pStr ^= wCryptKey;
        *pStr++ += byPlus;
    }
}
```

**3. CryptoPP Integration:**
- ECDSA signature verification
- SHA1 hashing
- RSA encryption for sensitive data

### Network Architecture

**Connection Types:**
- **ClientLine** (Port 27555): Player connections
- **AccountLine** (Port 27555): Account server
- **WebLine** (Port 27556): Web interface
- **BillingLine**: Payment processing

**Configuration:**
```cpp
struct _NET_TYPE_PARAM {
    char m_szModuleName[128];
    bool m_bRealSockCheck;
    uint8_t m_byRecvThreadNum;
    uint16_t m_wSocketMaxNum;
    uint32_t m_dwSendBufferSize;    // 1,000,000 bytes
    uint32_t m_dwRecvBufferSize;    // 1,000,000 bytes
};
```

## ⚖️ Game Balance Formulas

### Experience Calculation

**Base Formula:**
```cpp
void CPlayer::CalcExp(CCharacter* pDst, int nDam, CPartyModeKillMonsterExpNotify* kPartyExpNotify) {
    // Level difference calculation
    int nLevelDiff = pDst->GetLevel() - this->GetLevel();
    
    // Base experience from monster
    int nBaseExp = pMonster->GetBaseExp();
    
    // Apply level difference modifier
    float fLevelMod = GetLevelDiffModifier(nLevelDiff);
    
    // Apply damage contribution
    float fDamageRatio = (float)nDam / pDst->GetMaxHP();
    
    // Final experience
    int nFinalExp = (int)(nBaseExp * fLevelMod * fDamageRatio);
}
```

### Damage Calculation

**Attack Damage Formula:**
```cpp
int CCharacter::GetAttackDamPoint(int nAttPnt, int nAttPart, int nTolType, 
                                  CCharacter* pDst, bool bBackAttack) {
    // Base attack power
    int nBaseDamage = nAttPnt;
    
    // Apply part modifier (head, body, limbs)
    float fPartMod = GetPartModifier(nAttPart);
    
    // Apply tolerance (armor/defense)
    float fDefMod = GetDefenseModifier(pDst, nTolType);
    
    // Back attack bonus
    float fBackMod = bBackAttack ? 1.5f : 1.0f;
    
    // Final damage
    return (int)(nBaseDamage * fPartMod * fDefMod * fBackMod);
}
```

### Drop Rate System

**Level-Based Drop Rates:**
```cpp
uint32_t MonsterSetInfoData::GetMonsterDropRate(int iDiffLevel) {
    if (iDiffLevel == 0) {
        return m_iMonsterLootRateSame;          // Same level
    } else if (iDiffLevel > 0) {
        int idx = min(iDiffLevel, 10);
        return m_iMonsterLootingRateUp[idx];    // Higher level
    } else {
        int idx = min(abs(iDiffLevel), 10);
        return m_iMonsterLootingRateDown[idx];  // Lower level
    }
}
```

## 🚨 Critical Security Vulnerabilities

### 1. Buffer Overflow Vulnerabilities (CRITICAL)

**SQL Query Construction:**
```cpp
// VULNERABLE: No bounds checking
sprintf(&Dest, "{ CALL pInsert_PvpOrderView( %d ) }", dwSerial);
sprintf(&Dest, "{ CALL pInsert_pplirecord( %d ) }", dwSerial);
```

**String Operations:**
```cpp
// VULNERABLE: No length validation
strcpy_0(v6->m_szName, lhs->m_szName);
memcpy_0(v9 + 68, Str, (unsigned int)Size);
```

### 2. SQL Injection Vulnerabilities (CRITICAL)

**Direct String Interpolation:**
```cpp
sprintf_s(DstBuf, tBufferSize,
    "exec prc_rfonline_use @s_userid = '%s', @s_gcode = 'RFO',"
    "@s_pscode='%s', @s_price=%d, @s_server='%s', @s_character='%s'",
    rParam->in_szAcc, rParam->in_item[nIdx].in_strItemCode,
    rParam->in_item[nIdx].in_nPrice, rParam->in_szSvrName, 
    rParam->in_szAvatorName);
```

**Risk:** User-controlled input directly inserted into SQL without sanitization.

### 3. Authentication Bypass (MEDIUM)

**Client Version Check Bypass:**
```cpp
// VULNERABLE: Hardcoded bypass
if (strcmp_0(CMainThread::ms_szClientVerCheck, "X") && 
    strncmp(CMainThread::ms_szClientVerCheck, v11 + 25, 0x20ui64)) {
    // Version check failed
}
```

**Risk:** Setting client version check to "X" bypasses validation.

### 4. Weak Cryptography (MEDIUM)

**Predictable Random Keys:**
```cpp
// VULNERABLE: Weak randomness
for (int j = 0; j < 4; ++j) {
    int v9 = rand();
    pdwCode[j] = rand() + (v9 << 16);
}
```

**Risk:** Speed hack keys are predictable due to weak PRNG.

### 5. Memory Corruption (HIGH)

**Stack Buffer Overflow:**
```cpp
// VULNERABLE: Insufficient stack validation
bool _AtlVerifyStackAvailable(unsigned __int64 Size) {
    // Inadequate stack space checking
    // Risk of stack corruption
}
```

## 🛡️ Recommended Security Mitigations

### Immediate Actions (Critical)

1. **Replace sprintf with sprintf_s:**
   ```cpp
   // Instead of: sprintf(&Dest, "{ CALL pInsert_PvpOrderView( %d ) }", dwSerial);
   sprintf_s(Dest, sizeof(Dest), "{ CALL pInsert_PvpOrderView( %d ) }", dwSerial);
   ```

2. **Implement SQL Parameter Binding:**
   ```cpp
   // Use parameterized queries instead of string concatenation
   PreparedStatement* stmt = connection->prepareStatement(
       "EXEC prc_rfonline_use @s_userid = ?, @s_gcode = ?, @s_pscode = ?");
   stmt->setString(1, userId);
   stmt->setString(2, gameCode);
   stmt->setString(3, productCode);
   ```

3. **Replace strcpy with strcpy_s:**
   ```cpp
   // Instead of: strcpy_0(v6->m_szName, lhs->m_szName);
   strcpy_s(v6->m_szName, sizeof(v6->m_szName), lhs->m_szName);
   ```

### Long-term Improvements

1. **Implement Cryptographically Secure Random Number Generation:**
   ```cpp
   #include <random>
   std::random_device rd;
   std::mt19937 gen(rd());
   std::uniform_int_distribution<uint32_t> dis;
   
   for (int j = 0; j < 4; ++j) {
       pdwCode[j] = dis(gen);
   }
   ```

2. **Add Input Validation:**
   ```cpp
   bool ValidateUserInput(const char* input, size_t maxLength) {
       if (!input || strlen(input) >= maxLength) return false;
       // Additional validation logic
       return true;
   }
   ```

3. **Implement Rate Limiting:**
   ```cpp
   class RateLimiter {
       std::unordered_map<uint32_t, uint32_t> m_requestCounts;
       uint32_t m_maxRequestsPerMinute = 60;
   public:
       bool IsAllowed(uint32_t clientId);
   };
   ```

## 📊 Impact Assessment

| Vulnerability Type | Severity | Exploitability | Impact |
|-------------------|----------|----------------|---------|
| Buffer Overflow | Critical | High | Remote Code Execution |
| SQL Injection | Critical | High | Database Compromise |
| Auth Bypass | Medium | Medium | Unauthorized Access |
| Weak Crypto | Medium | Low | Predictable Keys |
| Memory Corruption | High | Medium | Server Crash/RCE |

## 🔍 Detection Signatures

**SQL Injection Attempts:**
- Monitor for SQL keywords in user input: `SELECT`, `UNION`, `DROP`, `INSERT`
- Watch for escape character attempts: `'`, `"`, `;`, `--`

**Buffer Overflow Attempts:**
- Monitor for oversized packets
- Watch for format string attacks: `%s`, `%x`, `%n`

**Authentication Bypass:**
- Monitor for client version "X"
- Watch for unusual login patterns

This analysis provides a foundation for securing the RF Online server infrastructure and understanding its internal mechanisms for further development or security hardening.
