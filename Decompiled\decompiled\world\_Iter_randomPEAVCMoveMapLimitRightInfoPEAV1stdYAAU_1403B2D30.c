/*
 * Function: ??$_Iter_random@PEAVCMoveMapLimitRightInfo@@PEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAVCMoveMapLimitRightInfo@@0@Z
 * Address: 0x1403B2D30
 */

CMoveMapLimitRightInfo *const *__fastcall std::_Iter_random<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(CMoveMapLimitRightInfo *const *__formal, CMoveMapLimitRightInfo *const *a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  CMoveMapLimitRightInfo *const *v6; // [sp+50h] [bp+8h]@1

  v6 = __formal;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return v6;
}
