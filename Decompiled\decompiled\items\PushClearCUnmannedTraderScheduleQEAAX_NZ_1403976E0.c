/*
 * Function: ?Push<PERSON>lear@CUnmannedTraderSchedule@@QEAAX_N@Z
 * Address: 0x1403976E0
 */

void __fastcall CUnmannedTraderSchedule::PushClear(CUnmannedTraderSchedule *this, bool bTimeLimit)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v4; // rax@5
  char *v5; // rax@13
  __int64 v6; // [sp+0h] [bp-D8h]@1
  CPlayer *pkOwner; // [sp+38h] [bp-A0h]@4
  CUnmannedTraderItemState::STATE v8; // [sp+44h] [bp-94h]@4
  char Dst; // [sp+60h] [bp-78h]@12
  unsigned int v10; // [sp+64h] [bp-74h]@12
  char v11; // [sp+68h] [bp-70h]@12
  unsigned int v12; // [sp+6Ch] [bp-6Ch]@12
  char v13; // [sp+70h] [bp-68h]@12
  unsigned __int16 v14; // [sp+72h] [bp-66h]@12
  char v15; // [sp+74h] [bp-64h]@13
  char v16; // [sp+81h] [bp-57h]@13
  unsigned __int64 v17; // [sp+C0h] [bp-18h]@4
  CUnmannedTraderSchedule *v18; // [sp+E0h] [bp+8h]@1

  v18 = this;
  v2 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  v18->m_eState = 2;
  pkOwner = 0i64;
  v8 = -1;
  if ( bTimeLimit )
  {
    v8 = 13;
  }
  else
  {
    v4 = CUnmannedTraderUserInfoTable::Instance();
    v8 = CUnmannedTraderUserInfoTable::GetCloseItemForPassTimeUpdateInfo(
           v4,
           v18->m_dwOwnerSerial,
           v18->m_dwRegistSerial,
           &pkOwner);
  }
  if ( v8 != -1 && (v8 == 6 || v8 == 13 || v8 == 11) )
  {
    memset_0(&Dst, 0, 0x50ui64);
    Dst = v18->m_byType;
    v10 = v18->m_dwRegistSerial;
    v11 = v8;
    v12 = v18->m_dwOwnerSerial;
    v13 = v18->m_byItemTableCode;
    v14 = v18->m_wItemTableIndex;
    if ( pkOwner )
    {
      strcpy_s(&v15, 0xDui64, pkOwner->m_pUserDB->m_szAccountID);
      v5 = CPlayerDB::GetCharNameW(&pkOwner->m_Param);
      strcpy_s(&v16, 0x11ui64, v5);
    }
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 62, &Dst, 80);
  }
}
