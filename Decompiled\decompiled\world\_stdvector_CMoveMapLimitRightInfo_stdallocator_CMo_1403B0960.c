/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::erase_::_1_::dtor$2
 * Address: 0x1403B0960
 */

void __fastcall std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(*(std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > **)(a2 + 88));
  }
}
