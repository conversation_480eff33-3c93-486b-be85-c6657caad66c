/*
 * Function: ?ProcGotoLimitZone@CMoveMapLimitInfoPortal@@AEAAEHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A47B0
 */

char __fastcall CMoveMapLimitInfoPortal::ProcGotoLimitZone(CMoveMapLimitInfoPortal *this, int iUserInx, char *pRequest, CMoveMapLimitRightInfo *pkRight)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  char v8; // [sp+30h] [bp-58h]@4
  char pbyType; // [sp+44h] [bp-44h]@5
  char v10; // [sp+45h] [bp-43h]@5
  char szMsg; // [sp+64h] [bp-24h]@5
  CMoveMapLimitInfoPortal *v12; // [sp+90h] [bp+8h]@1
  unsigned int iUserInxa; // [sp+98h] [bp+10h]@1

  iUserInxa = iUserInx;
  v12 = this;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = CMoveMapLimitInfoPortal::SubProcGotoLimitZone(v12, iUserInx, pRequest, pkRight);
  if ( v8 )
  {
    pbyType = 4;
    v10 = 40;
    szMsg = v8;
    CNetProcess::LoadSendMsg(unk_1414F2088, iUserInxa, &pbyType, &szMsg, 1u);
  }
  return 0;
}
