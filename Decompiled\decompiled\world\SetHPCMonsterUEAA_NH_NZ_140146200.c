/*
 * Function: ?SetHP@CMonster@@UEAA_NH_N@Z
 * Address: 0x140146200
 */

char __fastcall CMonster::SetHP(CMonster *this, int nHP, bool bOver)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@10
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMonster *v7; // [sp+30h] [bp+8h]@1
  int v8; // [sp+38h] [bp+10h]@1

  v8 = nHP;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nHP < 0 )
    v8 = 0;
  if ( !bOver && v8 > ((int (__fastcall *)(CMonster *))v7->vfptr->GetMaxHP)(v7) )
    v8 = ((int (__fastcall *)(CMonster *))v7->vfptr->GetMaxHP)(v7);
  if ( v7->m_nHP == v8 )
  {
    result = 0;
  }
  else
  {
    v7->m_nHP = v8;
    result = 1;
  }
  return result;
}
