/*
 * Function: ?ProcForceMoveHQ@CMoveMapLimitInfoPortal@@AEAAEHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A44B0
 */

char __fastcall CMoveMapLimitInfoPortal::ProcForceMoveHQ(CMoveMapLimitInfoPortal *this, int iUserInx, char *pRequest, CMoveMapLimitRightInfo *pkRight)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CMoveMapLimitInfoPortal *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8->m_uiProcNotifyInx = 0;
  CMyTimer::BeginTimer(v8->m_pkNotifyForceMoveHQTimer, 0xFA0u);
  v8->m_eNotifyForceMoveHQState = 1;
  return 0;
}
