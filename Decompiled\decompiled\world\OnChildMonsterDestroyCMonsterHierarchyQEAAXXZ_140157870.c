/*
 * Function: ?OnChildMonsterDestroy@CMonsterHierarchy@@QEAAXXZ
 * Address: 0x140157870
 */

void __fastcall CMonsterHierarchy::OnChildMonsterDestroy(CMonsterHierarchy *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@6
  unsigned int k; // [sp+24h] [bp-14h]@8
  CMonsterHierarchy *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_pParentMon )
    CMonsterHierarchy::PopChildMon(&v6->m_pParentMon->m_MonHierarcy, v6->m_pThisMon);
  for ( j = 0; j < 3; ++j )
  {
    for ( k = 0; k < 0xA; ++k )
    {
      if ( v6->m_pChildMon[j][k] )
        CMonster::Command_ChildMonDestroy(v6->m_pChildMon[j][k], 0x1388u);
    }
  }
  CMonsterHierarchy::Init(v6);
}
