/*
 * Function: _dynamic_atexit_destructor_for__g_strMapMap__
 * Address: 0x1406E8680
 */

void __cdecl dynamic_atexit_destructor_for__g_strMapMap__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>>::~map<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>>(&g_strMapMap);
}
